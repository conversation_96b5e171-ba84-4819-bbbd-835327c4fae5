const { validationResult } = require('express-validator')
const path = require('path')
const fs = require('fs')
const Artwork = require('../models/Artwork')
const ipfsService = require('../services/ipfsService')
const { cleanupTempFiles } = require('../middleware/upload')
const logger = require('../utils/logger')

/**
 * Upload artwork files and metadata to IPFS
 */
const uploadArtwork = async (req, res) => {
  let uploadedFiles = []
  
  try {
    // Check validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      // Cleanup uploaded files on validation error
      if (req.files) cleanupTempFiles(Object.values(req.files).flat())
      
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const user = req.user
    const { title, description, category, tags, attributes, unityData } = req.body
    
    // Parse JSON fields if they're strings
    const parsedTags = typeof tags === 'string' ? JSON.parse(tags) : tags
    const parsedAttributes = typeof attributes === 'string' ? JSON.parse(attributes) : attributes
    const parsedUnityData = typeof unityData === 'string' ? JSON.parse(unityData) : unityData

    // Check if files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      })
    }

    // Create artwork document
    const artwork = new Artwork({
      title,
      description,
      creator: user._id,
      category: category || '2D',
      attributes: {
        ...parsedAttributes,
        tags: parsedTags || []
      },
      unityData: parsedUnityData,
      status: 'uploading'
    })

    await artwork.save()
    await artwork.addProcessingLog('created', 'success', 'Artwork document created')

    // Process uploaded files
    const { mainFile, thumbnail, additional } = req.files

    // Upload main file to IPFS
    if (mainFile && mainFile[0]) {
      const file = mainFile[0]
      uploadedFiles.push(file)
      
      await artwork.addProcessingLog('main_file_upload', 'processing', 'Uploading main file to IPFS')
      
      const ipfsResult = await ipfsService.uploadFile(file.path, {
        pinataMetadata: {
          name: `${artwork._id}_main_${file.originalname}`,
          keyvalues: {
            artworkId: artwork._id.toString(),
            creator: user._id.toString(),
            fileType: 'main'
          }
        }
      })

      artwork.files.main = {
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        ipfsHash: ipfsResult.ipfsHash,
        ipfsUrl: ipfsResult.ipfsUrl
      }
      
      artwork.pinningStatus.main = 'pinned'
      await artwork.addProcessingLog('main_file_upload', 'success', `Main file uploaded: ${ipfsResult.ipfsHash}`)
    }

    // Upload thumbnail to IPFS
    if (thumbnail && thumbnail[0]) {
      const file = thumbnail[0]
      uploadedFiles.push(file)
      
      await artwork.addProcessingLog('thumbnail_upload', 'processing', 'Uploading thumbnail to IPFS')
      
      const ipfsResult = await ipfsService.uploadFile(file.path, {
        pinataMetadata: {
          name: `${artwork._id}_thumbnail_${file.originalname}`,
          keyvalues: {
            artworkId: artwork._id.toString(),
            creator: user._id.toString(),
            fileType: 'thumbnail'
          }
        }
      })

      artwork.files.thumbnail = {
        originalName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        ipfsHash: ipfsResult.ipfsHash,
        ipfsUrl: ipfsResult.ipfsUrl
      }
      
      artwork.pinningStatus.thumbnail = 'pinned'
      await artwork.addProcessingLog('thumbnail_upload', 'success', `Thumbnail uploaded: ${ipfsResult.ipfsHash}`)
    }

    // Upload additional files to IPFS
    if (additional && additional.length > 0) {
      uploadedFiles.push(...additional)
      
      for (let i = 0; i < additional.length; i++) {
        const file = additional[i]
        
        await artwork.addProcessingLog('additional_file_upload', 'processing', `Uploading additional file ${i + 1}`)
        
        const ipfsResult = await ipfsService.uploadFile(file.path, {
          pinataMetadata: {
            name: `${artwork._id}_additional_${i}_${file.originalname}`,
            keyvalues: {
              artworkId: artwork._id.toString(),
              creator: user._id.toString(),
              fileType: 'additional'
            }
          }
        })

        artwork.files.additional.push({
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          ipfsHash: ipfsResult.ipfsHash,
          ipfsUrl: ipfsResult.ipfsUrl,
          fileType: 'additional'
        })
        
        await artwork.addProcessingLog('additional_file_upload', 'success', `Additional file ${i + 1} uploaded: ${ipfsResult.ipfsHash}`)
      }
    }

    // Generate and upload NFT metadata
    await artwork.addProcessingLog('metadata_generation', 'processing', 'Generating NFT metadata')
    
    const nftMetadata = ipfsService.generateNFTMetadata(artwork, {
      externalUrl: `${req.protocol}://${req.get('host')}/api/artwork/${artwork._id}`
    })

    const metadataResult = await ipfsService.uploadJSON(nftMetadata, {
      pinataMetadata: {
        name: `${artwork._id}_metadata.json`,
        keyvalues: {
          artworkId: artwork._id.toString(),
          creator: user._id.toString(),
          fileType: 'metadata'
        }
      }
    })

    artwork.metadata = {
      ipfsHash: metadataResult.ipfsHash,
      ipfsUrl: metadataResult.ipfsUrl,
      content: nftMetadata
    }
    
    artwork.pinningStatus.metadata = 'pinned'
    artwork.status = 'ready'
    
    await artwork.addProcessingLog('metadata_generation', 'success', `Metadata uploaded: ${metadataResult.ipfsHash}`)
    await artwork.addProcessingLog('processing_complete', 'success', 'Artwork processing completed successfully')

    await artwork.save()

    // Cleanup temporary files
    cleanupTempFiles(uploadedFiles)

    logger.info(`Artwork uploaded successfully: ${artwork._id} by ${user.email}`)

    res.status(201).json({
      success: true,
      message: 'Artwork uploaded successfully',
      data: {
        artwork,
        ipfsHashes: {
          main: artwork.files.main?.ipfsHash,
          thumbnail: artwork.files.thumbnail?.ipfsHash,
          metadata: artwork.metadata.ipfsHash
        }
      }
    })
  } catch (error) {
    logger.error('Artwork upload error:', error)
    
    // Cleanup temporary files on error
    cleanupTempFiles(uploadedFiles)
    
    // Update artwork status if it was created
    if (req.body.artworkId) {
      try {
        const artwork = await Artwork.findById(req.body.artworkId)
        if (artwork) {
          artwork.status = 'failed'
          await artwork.addProcessingLog('upload_failed', 'error', error.message)
          await artwork.save()
        }
      } catch (updateError) {
        logger.error('Failed to update artwork status:', updateError)
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to upload artwork',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    })
  }
}

/**
 * Get user's artworks
 */
const getUserArtworks = async (req, res) => {
  try {
    const user = req.user
    const {
      status,
      category,
      visibility = 'private',
      page = 1,
      limit = 20,
      sort = '-createdAt'
    } = req.query

    const artworks = await Artwork.findByCreator(user._id, {
      status,
      category,
      visibility,
      page: parseInt(page),
      limit: parseInt(limit),
      sort
    })

    const total = await Artwork.countDocuments({
      creator: user._id,
      ...(status && { status }),
      ...(category && { category }),
      ...(visibility !== 'all' && { visibility })
    })

    res.json({
      success: true,
      data: {
        artworks,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Get user artworks error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch artworks'
    })
  }
}

/**
 * Get specific artwork by ID
 */
const getArtworkById = async (req, res) => {
  try {
    const { id } = req.params
    const user = req.user

    const artwork = await Artwork.findById(id).populate('creator', 'username email profile.firstName profile.lastName')

    if (!artwork) {
      return res.status(404).json({
        success: false,
        message: 'Artwork not found'
      })
    }

    // Check if user has permission to view this artwork
    if (artwork.creator._id.toString() !== user._id.toString() && artwork.visibility === 'private') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      })
    }

    // Increment view count if not the creator
    if (artwork.creator._id.toString() !== user._id.toString()) {
      artwork.stats.views += 1
      await artwork.save()
    }

    res.json({
      success: true,
      data: {
        artwork
      }
    })
  } catch (error) {
    logger.error('Get artwork by ID error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch artwork'
    })
  }
}

/**
 * Update artwork metadata
 */
const updateArtwork = async (req, res) => {
  try {
    const { id } = req.params
    const user = req.user
    const { title, description, visibility, tags, attributes } = req.body

    const artwork = await Artwork.findById(id)

    if (!artwork) {
      return res.status(404).json({
        success: false,
        message: 'Artwork not found'
      })
    }

    // Check if user owns this artwork
    if (artwork.creator.toString() !== user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      })
    }

    // Update fields
    if (title !== undefined) artwork.title = title
    if (description !== undefined) artwork.description = description
    if (visibility !== undefined) artwork.visibility = visibility
    if (tags !== undefined) artwork.attributes.tags = tags
    if (attributes !== undefined) {
      artwork.attributes = { ...artwork.attributes, ...attributes }
    }

    await artwork.save()

    logger.info(`Artwork updated: ${artwork._id} by ${user.email}`)

    res.json({
      success: true,
      message: 'Artwork updated successfully',
      data: {
        artwork
      }
    })
  } catch (error) {
    logger.error('Update artwork error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update artwork'
    })
  }
}

/**
 * Delete artwork
 */
const deleteArtwork = async (req, res) => {
  try {
    const { id } = req.params
    const user = req.user

    const artwork = await Artwork.findById(id)

    if (!artwork) {
      return res.status(404).json({
        success: false,
        message: 'Artwork not found'
      })
    }

    // Check if user owns this artwork
    if (artwork.creator.toString() !== user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      })
    }

    // Check if artwork is minted
    if (artwork.nft.isMinted) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete minted artwork'
      })
    }

    // Unpin files from IPFS (optional - you might want to keep them)
    try {
      if (artwork.files.main?.ipfsHash) {
        await ipfsService.unpinFile(artwork.files.main.ipfsHash)
      }
      if (artwork.files.thumbnail?.ipfsHash) {
        await ipfsService.unpinFile(artwork.files.thumbnail.ipfsHash)
      }
      if (artwork.metadata?.ipfsHash) {
        await ipfsService.unpinFile(artwork.metadata.ipfsHash)
      }
      if (artwork.files.additional) {
        for (const file of artwork.files.additional) {
          if (file.ipfsHash) {
            await ipfsService.unpinFile(file.ipfsHash)
          }
        }
      }
    } catch (unpinError) {
      logger.warn('Failed to unpin some files from IPFS:', unpinError)
    }

    await Artwork.findByIdAndDelete(id)

    logger.info(`Artwork deleted: ${id} by ${user.email}`)

    res.json({
      success: true,
      message: 'Artwork deleted successfully'
    })
  } catch (error) {
    logger.error('Delete artwork error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete artwork'
    })
  }
}

/**
 * Get public artworks (gallery)
 */
const getPublicArtworks = async (req, res) => {
  try {
    const {
      category,
      page = 1,
      limit = 20,
      sort = '-createdAt',
      search
    } = req.query

    const artworks = await Artwork.findPublic({
      category,
      page: parseInt(page),
      limit: parseInt(limit),
      sort,
      search
    })

    const total = await Artwork.countDocuments({
      visibility: 'public',
      status: 'ready',
      ...(category && { category }),
      ...(search && { $text: { $search: search } })
    })

    res.json({
      success: true,
      data: {
        artworks,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Get public artworks error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch public artworks'
    })
  }
}

/**
 * Get artwork statistics
 */
const getArtworkStats = async (req, res) => {
  try {
    const user = req.user

    const stats = await Artwork.getStats(user._id)

    res.json({
      success: true,
      data: {
        stats: stats[0] || {
          totalArtworks: 0,
          totalMinted: 0,
          totalSize: 0,
          byCategory: []
        }
      }
    })
  } catch (error) {
    logger.error('Get artwork stats error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch artwork statistics'
    })
  }
}

/**
 * Get IPFS file content
 */
const getIPFSContent = async (req, res) => {
  try {
    const { hash } = req.params

    // Validate IPFS hash format
    if (!/^Qm[1-9A-HJ-NP-Za-km-z]{44}$/.test(hash) && !/^bafy[a-z2-7]{55}$/.test(hash)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid IPFS hash format'
      })
    }

    const content = await ipfsService.getFileContent(hash)

    res.json({
      success: true,
      data: {
        content,
        ipfsUrl: ipfsService.getGatewayUrl(hash)
      }
    })
  } catch (error) {
    logger.error('Get IPFS content error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch IPFS content'
    })
  }
}

/**
 * Test IPFS connection
 */
const testIPFS = async (req, res) => {
  try {
    const result = await ipfsService.testConnection()

    res.json({
      success: result.success,
      message: result.success ? 'IPFS connection successful' : 'IPFS connection failed',
      data: result
    })
  } catch (error) {
    logger.error('Test IPFS error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to test IPFS connection'
    })
  }
}

module.exports = {
  uploadArtwork,
  getUserArtworks,
  getArtworkById,
  updateArtwork,
  deleteArtwork,
  getPublicArtworks,
  getArtworkStats,
  getIPFSContent,
  testIPFS
}
