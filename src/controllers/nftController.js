const { validationResult } = require('express-validator')
const Artwork = require('../models/Artwork')
const User = require('../models/User')
const blockchainService = require('../services/blockchainService')
const logger = require('../utils/logger')

/**
 * Mint NFT from artwork
 */
const mintNFT = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const user = req.user
    const { to, artworkId, gasLimit, gasPrice, network } = req.body

    // Find the artwork
    const artwork = await Artwork.findById(artworkId).populate('creator')

    if (!artwork) {
      return res.status(404).json({
        success: false,
        message: 'Artwork not found'
      })
    }

    // Check if user owns the artwork or is the creator
    if (artwork.creator._id.toString() !== user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only mint your own artworks'
      })
    }

    // Check if artwork is ready for minting
    if (artwork.status !== 'ready') {
      return res.status(400).json({
        success: false,
        message: `Artwork is not ready for minting. Current status: ${artwork.status}`
      })
    }

    // Check if already minted
    if (artwork.nft.isMinted) {
      return res.status(400).json({
        success: false,
        message: 'Artwork has already been minted as NFT',
        data: {
          tokenId: artwork.nft.tokenId,
          transactionHash: artwork.nft.transactionHash
        }
      })
    }

    // Check if metadata is available
    if (!artwork.metadata?.ipfsHash) {
      return res.status(400).json({
        success: false,
        message: 'Artwork metadata not available on IPFS'
      })
    }

    // Estimate gas first
    const gasEstimate = await blockchainService.estimateGas(
      user,
      'mint',
      [to, artwork.metadata.ipfsUrl, artwork.creator._id.toString()],
      network
    )

    // Check user balance
    const balance = await blockchainService.getBalance(user.wallet.address, network)
    const requiredAmount = BigInt(gasEstimate.totalCost.wei)
    const userBalance = BigInt(balance.wei)

    if (userBalance < requiredAmount) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance for minting',
        data: {
          required: gasEstimate.totalCost,
          available: balance
        }
      })
    }

    // Mint the NFT
    const mintResult = await blockchainService.mintNFT(
      user,
      to,
      artwork.metadata.ipfsUrl,
      artwork.creator._id.toString(),
      {
        network,
        gasLimit,
        gasPrice
      }
    )

    // Update artwork with minting information
    artwork.nft = {
      isMinted: false, // Will be set to true when transaction confirms
      tokenId: null, // Will be set when transaction confirms
      contractAddress: await (await blockchainService.getContract(network)).getAddress(),
      transactionHash: mintResult.transactionHash,
      mintedAt: new Date(),
      mintedTo: to,
      network: network || blockchainService.currentNetwork
    }

    await artwork.addProcessingLog('minting_started', 'processing', `NFT minting transaction submitted: ${mintResult.transactionHash}`)
    await artwork.save()

    // Update user stats
    user.stats.totalSpent = (BigInt(user.stats.totalSpent) + BigInt(gasEstimate.totalCost.wei)).toString()
    await user.save()

    logger.info(`NFT minting initiated for artwork ${artworkId} by ${user.email}`)

    res.status(200).json({
      success: true,
      message: 'NFT minting transaction submitted successfully',
      data: {
        transactionHash: mintResult.transactionHash,
        artwork: artwork,
        gasEstimate,
        mintResult
      }
    })

    // Monitor transaction in background
    monitorMintingTransaction(artwork._id, mintResult.transactionHash, network)

  } catch (error) {
    logger.error('NFT minting error:', error)
    
    res.status(500).json({
      success: false,
      message: 'Failed to mint NFT',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    })
  }
}

/**
 * Batch mint multiple NFTs
 */
const batchMintNFT = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const user = req.user
    const { to, artworkIds, gasLimit, gasPrice, network } = req.body

    // Find all artworks
    const artworks = await Artwork.find({
      _id: { $in: artworkIds },
      creator: user._id
    }).populate('creator')

    if (artworks.length !== artworkIds.length) {
      return res.status(404).json({
        success: false,
        message: 'One or more artworks not found or not owned by you'
      })
    }

    // Validate all artworks
    const invalidArtworks = artworks.filter(artwork => 
      artwork.status !== 'ready' || 
      artwork.nft.isMinted || 
      !artwork.metadata?.ipfsHash
    )

    if (invalidArtworks.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Some artworks are not ready for minting',
        data: {
          invalidArtworks: invalidArtworks.map(a => ({
            id: a._id,
            title: a.title,
            status: a.status,
            isMinted: a.nft.isMinted,
            hasMetadata: !!a.metadata?.ipfsHash
          }))
        }
      })
    }

    // Prepare token URIs
    const tokenURIs = artworks.map(artwork => artwork.metadata.ipfsUrl)

    // Estimate gas
    const gasEstimate = await blockchainService.estimateGas(
      user,
      'batchMint',
      [to, tokenURIs, user._id.toString()],
      network
    )

    // Check user balance
    const balance = await blockchainService.getBalance(user.wallet.address, network)
    const requiredAmount = BigInt(gasEstimate.totalCost.wei)
    const userBalance = BigInt(balance.wei)

    if (userBalance < requiredAmount) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance for batch minting',
        data: {
          required: gasEstimate.totalCost,
          available: balance
        }
      })
    }

    // Batch mint NFTs
    const mintResult = await blockchainService.batchMintNFT(
      user,
      to,
      tokenURIs,
      user._id.toString(),
      {
        network,
        gasLimit,
        gasPrice
      }
    )

    // Update all artworks
    const contractAddress = await (await blockchainService.getContract(network)).getAddress()
    
    for (const artwork of artworks) {
      artwork.nft = {
        isMinted: false,
        tokenId: null,
        contractAddress,
        transactionHash: mintResult.transactionHash,
        mintedAt: new Date(),
        mintedTo: to,
        network: network || blockchainService.currentNetwork
      }

      await artwork.addProcessingLog('batch_minting_started', 'processing', `Batch NFT minting transaction submitted: ${mintResult.transactionHash}`)
      await artwork.save()
    }

    // Update user stats
    user.stats.totalSpent = (BigInt(user.stats.totalSpent) + BigInt(gasEstimate.totalCost.wei)).toString()
    await user.save()

    logger.info(`Batch NFT minting initiated for ${artworkIds.length} artworks by ${user.email}`)

    res.status(200).json({
      success: true,
      message: 'Batch NFT minting transaction submitted successfully',
      data: {
        transactionHash: mintResult.transactionHash,
        artworks: artworks,
        gasEstimate,
        mintResult
      }
    })

    // Monitor transaction in background
    monitorBatchMintingTransaction(artworkIds, mintResult.transactionHash, network)

  } catch (error) {
    logger.error('Batch NFT minting error:', error)
    
    res.status(500).json({
      success: false,
      message: 'Failed to batch mint NFTs',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    })
  }
}

/**
 * Get transaction status
 */
const getTransactionStatus = async (req, res) => {
  try {
    const { transactionHash } = req.params
    const { network } = req.query

    const receipt = await blockchainService.getTransactionReceipt(transactionHash, network)

    res.json({
      success: true,
      data: {
        transactionHash,
        ...receipt
      }
    })
  } catch (error) {
    logger.error('Get transaction status error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get transaction status'
    })
  }
}

/**
 * Get user's minted NFTs
 */
const getUserNFTs = async (req, res) => {
  try {
    const user = req.user
    const { network } = req.query

    const nfts = await blockchainService.getUserNFTs(user.wallet.address, network)

    // Get corresponding artworks
    const artworksWithNFTs = await Artwork.find({
      'nft.isMinted': true,
      creator: user._id
    }).populate('creator', 'username email profile.firstName profile.lastName')

    res.json({
      success: true,
      data: {
        nfts,
        artworks: artworksWithNFTs,
        totalCount: nfts.length
      }
    })
  } catch (error) {
    logger.error('Get user NFTs error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get user NFTs'
    })
  }
}

/**
 * Get contract information
 */
const getContractInfo = async (req, res) => {
  try {
    const { network } = req.query

    const contractInfo = await blockchainService.getContractInfo(network)

    res.json({
      success: true,
      data: contractInfo
    })
  } catch (error) {
    logger.error('Get contract info error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get contract information'
    })
  }
}

/**
 * Estimate minting gas
 */
const estimateMintingGas = async (req, res) => {
  try {
    const user = req.user
    const { to, artworkId, network } = req.body

    const artwork = await Artwork.findById(artworkId)

    if (!artwork) {
      return res.status(404).json({
        success: false,
        message: 'Artwork not found'
      })
    }

    if (!artwork.metadata?.ipfsUrl) {
      return res.status(400).json({
        success: false,
        message: 'Artwork metadata not available'
      })
    }

    const gasEstimate = await blockchainService.estimateGas(
      user,
      'mint',
      [to, artwork.metadata.ipfsUrl, artwork.creator.toString()],
      network
    )

    const balance = await blockchainService.getBalance(user.wallet.address, network)

    res.json({
      success: true,
      data: {
        gasEstimate,
        userBalance: balance,
        canAfford: BigInt(balance.wei) >= BigInt(gasEstimate.totalCost.wei)
      }
    })
  } catch (error) {
    logger.error('Gas estimation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to estimate gas'
    })
  }
}

/**
 * Monitor single NFT minting transaction
 */
async function monitorMintingTransaction(artworkId, transactionHash, network) {
  try {
    logger.info(`Monitoring minting transaction: ${transactionHash}`)

    const receipt = await blockchainService.monitorTransaction(transactionHash, network)

    const artwork = await Artwork.findById(artworkId)
    if (!artwork) {
      logger.error(`Artwork ${artworkId} not found during transaction monitoring`)
      return
    }

    if (receipt.status === 'success') {
      // Parse events to get token ID
      const mintEvent = receipt.events.find(event => event.name === 'TokenMinted')
      const tokenId = mintEvent ? mintEvent.args[0].toString() : null

      // Update artwork
      artwork.nft.isMinted = true
      artwork.nft.tokenId = tokenId
      artwork.status = 'ready'

      await artwork.addProcessingLog('minting_completed', 'success', `NFT minted successfully. Token ID: ${tokenId}`)

      // Update user stats
      const user = await User.findById(artwork.creator)
      if (user) {
        user.stats.totalMinted += 1
        await user.save()
      }

      logger.info(`NFT minting completed for artwork ${artworkId}. Token ID: ${tokenId}`)
    } else {
      artwork.nft.isMinted = false
      await artwork.addProcessingLog('minting_failed', 'error', 'NFT minting transaction failed')
      logger.error(`NFT minting failed for artwork ${artworkId}`)
    }

    await artwork.save()
  } catch (error) {
    logger.error('Error monitoring minting transaction:', error)

    try {
      const artwork = await Artwork.findById(artworkId)
      if (artwork) {
        await artwork.addProcessingLog('minting_error', 'error', `Monitoring error: ${error.message}`)
        await artwork.save()
      }
    } catch (updateError) {
      logger.error('Failed to update artwork with monitoring error:', updateError)
    }
  }
}

/**
 * Monitor batch NFT minting transaction
 */
async function monitorBatchMintingTransaction(artworkIds, transactionHash, network) {
  try {
    logger.info(`Monitoring batch minting transaction: ${transactionHash}`)

    const receipt = await blockchainService.monitorTransaction(transactionHash, network)

    const artworks = await Artwork.find({ _id: { $in: artworkIds } })

    if (receipt.status === 'success') {
      // Parse events to get token IDs
      const mintEvents = receipt.events.filter(event => event.name === 'TokenMinted')

      for (let i = 0; i < artworks.length; i++) {
        const artwork = artworks[i]
        const tokenId = mintEvents[i] ? mintEvents[i].args[0].toString() : null

        artwork.nft.isMinted = true
        artwork.nft.tokenId = tokenId
        artwork.status = 'ready'

        await artwork.addProcessingLog('batch_minting_completed', 'success', `NFT minted successfully in batch. Token ID: ${tokenId}`)
        await artwork.save()
      }

      // Update user stats
      const user = await User.findById(artworks[0].creator)
      if (user) {
        user.stats.totalMinted += artworks.length
        await user.save()
      }

      logger.info(`Batch NFT minting completed for ${artworks.length} artworks`)
    } else {
      for (const artwork of artworks) {
        artwork.nft.isMinted = false
        await artwork.addProcessingLog('batch_minting_failed', 'error', 'Batch NFT minting transaction failed')
        await artwork.save()
      }

      logger.error(`Batch NFT minting failed for ${artworks.length} artworks`)
    }
  } catch (error) {
    logger.error('Error monitoring batch minting transaction:', error)

    try {
      const artworks = await Artwork.find({ _id: { $in: artworkIds } })
      for (const artwork of artworks) {
        await artwork.addProcessingLog('batch_minting_error', 'error', `Monitoring error: ${error.message}`)
        await artwork.save()
      }
    } catch (updateError) {
      logger.error('Failed to update artworks with monitoring error:', updateError)
    }
  }
}

module.exports = {
  mintNFT,
  batchMintNFT,
  getTransactionStatus,
  getUserNFTs,
  getContractInfo,
  estimateMintingGas
}
