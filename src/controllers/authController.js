const { validationResult } = require('express-validator')
const User = require('../models/User')
const logger = require('../utils/logger')

/**
 * Register a new user
 */
const register = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { email, username, password, firstName, lastName } = req.body

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    })

    if (existingUser) {
      const field = existingUser.email === email ? 'email' : 'username'
      return res.status(400).json({
        success: false,
        message: `User with this ${field} already exists`
      })
    }

    // Create new user (wallet will be generated automatically)
    const user = new User({
      email,
      username,
      password,
      profile: {
        firstName: firstName || '',
        lastName: lastName || ''
      }
    })

    await user.save()

    // Generate tokens
    const token = user.generateAuthToken()
    const refreshToken = user.generateRefreshToken()
    await user.save() // Save refresh token

    logger.info(`New user registered: ${email}`)

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user,
        token,
        refreshToken,
        wallet: {
          address: user.wallet.address
        }
      }
    })
  } catch (error) {
    logger.error('Registration error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error during registration'
    })
  }
}

/**
 * Login user
 */
const login = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const { email, password } = req.body

    // Find user by credentials
    const user = await User.findByCredentials(email, password)

    // Generate tokens
    const token = user.generateAuthToken()
    const refreshToken = user.generateRefreshToken()
    await user.save() // Save refresh token

    logger.info(`User logged in: ${email}`)

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user,
        token,
        refreshToken,
        wallet: {
          address: user.wallet.address
        }
      }
    })
  } catch (error) {
    logger.error('Login error:', error)
    
    if (error.message.includes('Invalid login credentials') || 
        error.message.includes('Account is temporarily locked')) {
      return res.status(401).json({
        success: false,
        message: error.message
      })
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during login'
    })
  }
}

/**
 * Refresh access token
 */
const refreshToken = async (req, res) => {
  try {
    const user = req.user
    const oldRefreshToken = req.refreshToken

    // Remove old refresh token
    user.refreshTokens = user.refreshTokens.filter(t => t.token !== oldRefreshToken)

    // Generate new tokens
    const token = user.generateAuthToken()
    const refreshToken = user.generateRefreshToken()
    await user.save()

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token,
        refreshToken
      }
    })
  } catch (error) {
    logger.error('Token refresh error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error during token refresh'
    })
  }
}

/**
 * Logout user
 */
const logout = async (req, res) => {
  try {
    const user = req.user
    const token = req.token

    // Remove current refresh token if provided
    const { refreshToken } = req.body
    if (refreshToken) {
      user.refreshTokens = user.refreshTokens.filter(t => t.token !== refreshToken)
      await user.save()
    }

    logger.info(`User logged out: ${user.email}`)

    res.json({
      success: true,
      message: 'Logout successful'
    })
  } catch (error) {
    logger.error('Logout error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error during logout'
    })
  }
}

/**
 * Logout from all devices
 */
const logoutAll = async (req, res) => {
  try {
    const user = req.user

    // Clear all refresh tokens
    user.refreshTokens = []
    await user.save()

    logger.info(`User logged out from all devices: ${user.email}`)

    res.json({
      success: true,
      message: 'Logged out from all devices successfully'
    })
  } catch (error) {
    logger.error('Logout all error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error during logout'
    })
  }
}

/**
 * Get current user profile
 */
const getProfile = async (req, res) => {
  try {
    const user = req.user

    res.json({
      success: true,
      data: {
        user,
        wallet: {
          address: user.wallet.address
        }
      }
    })
  } catch (error) {
    logger.error('Get profile error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching profile'
    })
  }
}

/**
 * Update user profile
 */
const updateProfile = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const user = req.user
    const { firstName, lastName, bio } = req.body

    // Update profile fields
    if (firstName !== undefined) user.profile.firstName = firstName
    if (lastName !== undefined) user.profile.lastName = lastName
    if (bio !== undefined) user.profile.bio = bio

    await user.save()

    logger.info(`User profile updated: ${user.email}`)

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user
      }
    })
  } catch (error) {
    logger.error('Update profile error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error while updating profile'
    })
  }
}

/**
 * Change password
 */
const changePassword = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      })
    }

    const user = req.user
    const { currentPassword, newPassword } = req.body

    // Verify current password
    const isMatch = await user.comparePassword(currentPassword)
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      })
    }

    // Update password
    user.password = newPassword
    await user.save()

    // Clear all refresh tokens to force re-login
    user.refreshTokens = []
    await user.save()

    logger.info(`Password changed for user: ${user.email}`)

    res.json({
      success: true,
      message: 'Password changed successfully. Please login again.'
    })
  } catch (error) {
    logger.error('Change password error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error while changing password'
    })
  }
}

/**
 * Deactivate account
 */
const deactivateAccount = async (req, res) => {
  try {
    const user = req.user
    const { password } = req.body

    // Verify password
    const isMatch = await user.comparePassword(password)
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: 'Password is incorrect'
      })
    }

    // Deactivate account
    user.isActive = false
    user.refreshTokens = []
    await user.save()

    logger.info(`Account deactivated: ${user.email}`)

    res.json({
      success: true,
      message: 'Account deactivated successfully'
    })
  } catch (error) {
    logger.error('Deactivate account error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error while deactivating account'
    })
  }
}

module.exports = {
  register,
  login,
  refreshToken,
  logout,
  logoutAll,
  getProfile,
  updateProfile,
  changePassword,
  deactivateAccount
}
