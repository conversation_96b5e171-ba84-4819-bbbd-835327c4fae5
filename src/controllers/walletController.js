const { ethers } = require('ethers')
const User = require('../models/User')
const logger = require('../utils/logger')

// Initialize provider
const getProvider = () => {
  const rpcUrl = process.env.NODE_ENV === 'production' 
    ? process.env.POLYGON_RPC_URL 
    : process.env.MUMBAI_RPC_URL
  
  return new ethers.JsonRpcProvider(rpcUrl)
}

/**
 * Get wallet information
 */
const getWalletInfo = async (req, res) => {
  try {
    const user = req.user
    const provider = getProvider()

    // Get wallet balance
    const balance = await provider.getBalance(user.wallet.address)
    const balanceInMatic = ethers.formatEther(balance)

    // Get transaction count (nonce)
    const transactionCount = await provider.getTransactionCount(user.wallet.address)

    // Get network information
    const network = await provider.getNetwork()

    res.json({
      success: true,
      data: {
        address: user.wallet.address,
        balance: {
          wei: balance.toString(),
          matic: balanceInMatic
        },
        transactionCount,
        network: {
          name: network.name,
          chainId: network.chainId.toString()
        }
      }
    })
  } catch (error) {
    logger.error('Get wallet info error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch wallet information'
    })
  }
}

/**
 * Get transaction history
 */
const getTransactionHistory = async (req, res) => {
  try {
    const user = req.user
    const provider = getProvider()
    const { page = 1, limit = 20 } = req.query

    // Note: This is a basic implementation
    // For production, you should use a service like Moralis, Alchemy, or Etherscan API
    // to get comprehensive transaction history
    
    const currentBlock = await provider.getBlockNumber()
    const fromBlock = Math.max(0, currentBlock - 10000) // Last ~10k blocks
    
    // Get sent transactions (this is limited and not comprehensive)
    const sentTxs = []
    
    // For a more complete solution, integrate with:
    // - Moralis API
    // - Alchemy API  
    // - Etherscan/Polygonscan API
    // - The Graph Protocol

    res.json({
      success: true,
      data: {
        transactions: sentTxs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: sentTxs.length
        }
      }
    })
  } catch (error) {
    logger.error('Get transaction history error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction history'
    })
  }
}

/**
 * Estimate gas for a transaction
 */
const estimateGas = async (req, res) => {
  try {
    const user = req.user
    const { to, value, data } = req.body
    const provider = getProvider()

    // Validate inputs
    if (!ethers.isAddress(to)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid recipient address'
      })
    }

    // Create transaction object
    const transaction = {
      from: user.wallet.address,
      to,
      value: value ? ethers.parseEther(value.toString()) : 0,
      data: data || '0x'
    }

    // Estimate gas
    const gasEstimate = await provider.estimateGas(transaction)
    
    // Get current gas price
    const feeData = await provider.getFeeData()
    
    // Calculate total cost
    const totalCost = gasEstimate * feeData.gasPrice

    res.json({
      success: true,
      data: {
        gasEstimate: gasEstimate.toString(),
        gasPrice: feeData.gasPrice.toString(),
        maxFeePerGas: feeData.maxFeePerGas?.toString(),
        maxPriorityFeePerGas: feeData.maxPriorityFeePerGas?.toString(),
        totalCost: {
          wei: totalCost.toString(),
          matic: ethers.formatEther(totalCost)
        }
      }
    })
  } catch (error) {
    logger.error('Gas estimation error:', error)
    
    if (error.code === 'INSUFFICIENT_FUNDS') {
      return res.status(400).json({
        success: false,
        message: 'Insufficient funds for transaction'
      })
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to estimate gas'
    })
  }
}

/**
 * Send transaction (for testing purposes - be very careful with this)
 */
const sendTransaction = async (req, res) => {
  try {
    const user = req.user
    const { to, value, gasLimit, gasPrice } = req.body
    const provider = getProvider()

    // Validate inputs
    if (!ethers.isAddress(to)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid recipient address'
      })
    }

    // Get user's wallet
    const wallet = user.getWallet().connect(provider)

    // Create transaction
    const transaction = {
      to,
      value: ethers.parseEther(value.toString()),
      gasLimit: gasLimit || 21000,
      gasPrice: gasPrice ? ethers.parseUnits(gasPrice.toString(), 'gwei') : undefined
    }

    // Send transaction
    const txResponse = await wallet.sendTransaction(transaction)
    
    logger.info(`Transaction sent by ${user.email}: ${txResponse.hash}`)

    res.json({
      success: true,
      message: 'Transaction sent successfully',
      data: {
        transactionHash: txResponse.hash,
        from: txResponse.from,
        to: txResponse.to,
        value: ethers.formatEther(txResponse.value),
        gasLimit: txResponse.gasLimit.toString(),
        gasPrice: txResponse.gasPrice.toString(),
        nonce: txResponse.nonce
      }
    })
  } catch (error) {
    logger.error('Send transaction error:', error)
    
    if (error.code === 'INSUFFICIENT_FUNDS') {
      return res.status(400).json({
        success: false,
        message: 'Insufficient funds for transaction'
      })
    }
    
    if (error.code === 'NONCE_EXPIRED') {
      return res.status(400).json({
        success: false,
        message: 'Transaction nonce expired'
      })
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to send transaction'
    })
  }
}

/**
 * Get transaction receipt
 */
const getTransactionReceipt = async (req, res) => {
  try {
    const { transactionHash } = req.params
    const provider = getProvider()

    // Validate transaction hash
    if (!/^0x[a-fA-F0-9]{64}$/.test(transactionHash)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid transaction hash format'
      })
    }

    // Get transaction receipt
    const receipt = await provider.getTransactionReceipt(transactionHash)
    
    if (!receipt) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found or still pending'
      })
    }

    // Get transaction details
    const transaction = await provider.getTransaction(transactionHash)

    res.json({
      success: true,
      data: {
        transactionHash: receipt.hash,
        blockNumber: receipt.blockNumber,
        blockHash: receipt.blockHash,
        transactionIndex: receipt.index,
        from: receipt.from,
        to: receipt.to,
        gasUsed: receipt.gasUsed.toString(),
        gasPrice: transaction.gasPrice.toString(),
        status: receipt.status === 1 ? 'success' : 'failed',
        logs: receipt.logs,
        confirmations: await transaction.confirmations()
      }
    })
  } catch (error) {
    logger.error('Get transaction receipt error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction receipt'
    })
  }
}

/**
 * Get network status
 */
const getNetworkStatus = async (req, res) => {
  try {
    const provider = getProvider()

    // Get network information
    const network = await provider.getNetwork()
    const blockNumber = await provider.getBlockNumber()
    const feeData = await provider.getFeeData()

    res.json({
      success: true,
      data: {
        network: {
          name: network.name,
          chainId: network.chainId.toString()
        },
        blockNumber,
        gasPrice: {
          standard: feeData.gasPrice?.toString(),
          fast: feeData.maxFeePerGas?.toString(),
          priority: feeData.maxPriorityFeePerGas?.toString()
        }
      }
    })
  } catch (error) {
    logger.error('Get network status error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch network status'
    })
  }
}

module.exports = {
  getWalletInfo,
  getTransactionHistory,
  estimateGas,
  sendTransaction,
  getTransactionReceipt,
  getNetworkStatus
}
