const mongoose = require('mongoose')

const artworkSchema = new mongoose.Schema({
  // Basic artwork information
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Creator information
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Artwork type and category
  category: {
    type: String,
    enum: ['2D', '3D', 'Mixed'],
    default: '2D'
  },
  
  // File information
  files: {
    // Main artwork file (image or 3D model)
    main: {
      originalName: String,
      mimeType: String,
      size: Number,
      ipfsHash: String,
      ipfsUrl: String
    },
    // Thumbnail/preview image
    thumbnail: {
      originalName: String,
      mimeType: String,
      size: Number,
      ipfsHash: String,
      ipfsUrl: String
    },
    // Additional files (textures, materials, etc.)
    additional: [{
      originalName: String,
      mimeType: String,
      size: Number,
      ipfsHash: String,
      ipfsUrl: String,
      fileType: String // 'texture', 'material', 'animation', etc.
    }]
  },
  
  // IPFS metadata
  metadata: {
    // Main metadata JSON file
    ipfsHash: String,
    ipfsUrl: String,
    // Backup of metadata content
    content: {
      name: String,
      description: String,
      image: String,
      external_url: String,
      attributes: [{
        trait_type: String,
        value: mongoose.Schema.Types.Mixed,
        display_type: String
      }],
      animation_url: String,
      background_color: String
    }
  },
  
  // Artwork attributes and properties
  attributes: {
    dimensions: {
      width: Number,
      height: Number,
      depth: Number
    },
    colors: [String], // Dominant colors
    tags: [String],
    style: String,
    medium: String, // Digital, Mixed Reality, etc.
    complexity: {
      type: String,
      enum: ['Simple', 'Medium', 'Complex'],
      default: 'Medium'
    }
  },
  
  // Unity AR specific data
  unityData: {
    sceneId: String,
    objectCount: Number,
    renderSettings: {
      lighting: String,
      shadows: Boolean,
      postProcessing: Boolean
    },
    arSettings: {
      trackingMode: String,
      planeDetection: Boolean,
      lightEstimation: Boolean
    }
  },
  
  // Status and processing
  status: {
    type: String,
    enum: ['uploading', 'processing', 'ready', 'failed', 'archived'],
    default: 'uploading'
  },
  
  processingLog: [{
    step: String,
    status: String,
    message: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  
  // NFT minting information
  nft: {
    isMinted: {
      type: Boolean,
      default: false
    },
    tokenId: String,
    contractAddress: String,
    transactionHash: String,
    mintedAt: Date,
    mintedTo: String, // Wallet address
    network: {
      type: String,
      enum: ['polygon', 'mumbai', 'amoy'],
      default: 'mumbai'
    }
  },
  
  // Visibility and sharing
  visibility: {
    type: String,
    enum: ['private', 'public', 'unlisted'],
    default: 'private'
  },
  
  // Statistics
  stats: {
    views: {
      type: Number,
      default: 0
    },
    downloads: {
      type: Number,
      default: 0
    },
    likes: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    }
  },
  
  // File size totals
  totalSize: {
    type: Number,
    default: 0
  },
  
  // Pinning status for IPFS
  pinningStatus: {
    main: {
      type: String,
      enum: ['pending', 'pinned', 'failed'],
      default: 'pending'
    },
    metadata: {
      type: String,
      enum: ['pending', 'pinned', 'failed'],
      default: 'pending'
    },
    thumbnail: {
      type: String,
      enum: ['pending', 'pinned', 'failed'],
      default: 'pending'
    }
  }
}, {
  timestamps: true
})

// Indexes
artworkSchema.index({ creator: 1, createdAt: -1 })
artworkSchema.index({ status: 1 })
artworkSchema.index({ category: 1 })
artworkSchema.index({ 'nft.isMinted': 1 })
artworkSchema.index({ visibility: 1 })
artworkSchema.index({ title: 'text', description: 'text' })

// Virtual for total file count
artworkSchema.virtual('fileCount').get(function() {
  let count = 0
  if (this.files.main?.ipfsHash) count++
  if (this.files.thumbnail?.ipfsHash) count++
  if (this.files.additional) count += this.files.additional.length
  return count
})

// Virtual for IPFS gateway URLs
artworkSchema.virtual('gatewayUrls').get(function() {
  const gateway = 'https://gateway.pinata.cloud/ipfs/'
  return {
    main: this.files.main?.ipfsHash ? `${gateway}${this.files.main.ipfsHash}` : null,
    thumbnail: this.files.thumbnail?.ipfsHash ? `${gateway}${this.files.thumbnail.ipfsHash}` : null,
    metadata: this.metadata?.ipfsHash ? `${gateway}${this.metadata.ipfsHash}` : null
  }
})

// Pre-save middleware to calculate total size
artworkSchema.pre('save', function(next) {
  let totalSize = 0
  
  if (this.files.main?.size) totalSize += this.files.main.size
  if (this.files.thumbnail?.size) totalSize += this.files.thumbnail.size
  if (this.files.additional) {
    this.files.additional.forEach(file => {
      if (file.size) totalSize += file.size
    })
  }
  
  this.totalSize = totalSize
  next()
})

// Instance method to add processing log entry
artworkSchema.methods.addProcessingLog = function(step, status, message) {
  this.processingLog.push({
    step,
    status,
    message,
    timestamp: new Date()
  })
  return this.save()
}

// Instance method to update status
artworkSchema.methods.updateStatus = function(status, message) {
  this.status = status
  if (message) {
    this.addProcessingLog('status_update', status, message)
  }
  return this.save()
}

// Instance method to mark as minted
artworkSchema.methods.markAsMinted = function(tokenId, contractAddress, transactionHash, mintedTo, network = 'mumbai') {
  this.nft = {
    isMinted: true,
    tokenId,
    contractAddress,
    transactionHash,
    mintedAt: new Date(),
    mintedTo,
    network
  }
  this.status = 'ready'
  return this.save()
}

// Static method to find by creator
artworkSchema.statics.findByCreator = function(creatorId, options = {}) {
  const {
    status,
    category,
    visibility = 'private',
    page = 1,
    limit = 20,
    sort = '-createdAt'
  } = options
  
  const query = { creator: creatorId }
  
  if (status) query.status = status
  if (category) query.category = category
  if (visibility !== 'all') query.visibility = visibility
  
  return this.find(query)
    .sort(sort)
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('creator', 'username email profile.firstName profile.lastName')
}

// Static method to find public artworks
artworkSchema.statics.findPublic = function(options = {}) {
  const {
    category,
    page = 1,
    limit = 20,
    sort = '-createdAt',
    search
  } = options
  
  const query = { 
    visibility: 'public',
    status: 'ready'
  }
  
  if (category) query.category = category
  if (search) {
    query.$text = { $search: search }
  }
  
  return this.find(query)
    .sort(sort)
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('creator', 'username profile.firstName profile.lastName')
}

// Static method to get statistics
artworkSchema.statics.getStats = function(creatorId) {
  return this.aggregate([
    { $match: { creator: mongoose.Types.ObjectId(creatorId) } },
    {
      $group: {
        _id: null,
        totalArtworks: { $sum: 1 },
        totalMinted: { $sum: { $cond: ['$nft.isMinted', 1, 0] } },
        totalSize: { $sum: '$totalSize' },
        byCategory: {
          $push: {
            category: '$category',
            status: '$status'
          }
        }
      }
    }
  ])
}

// Remove sensitive data when converting to JSON
artworkSchema.methods.toJSON = function() {
  const artwork = this.toObject({ virtuals: true })
  
  // Add gateway URLs for easy access
  artwork.gatewayUrls = this.gatewayUrls
  
  return artwork
}

module.exports = mongoose.model('Artwork', artworkSchema)
