const mongoose = require('mongoose')

const transactionSchema = new mongoose.Schema({
  // Transaction identification
  transactionHash: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // User who initiated the transaction
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // Transaction type
  type: {
    type: String,
    enum: ['mint', 'batch_mint', 'transfer', 'approve', 'other'],
    required: true,
    index: true
  },
  
  // Network information
  network: {
    type: String,
    enum: ['polygon', 'mumbai', 'amoy'],
    required: true,
    default: 'amoy'
  },
  
  // Transaction status
  status: {
    type: String,
    enum: ['pending', 'success', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  
  // Block information
  blockNumber: {
    type: Number,
    index: true
  },
  
  blockHash: String,
  
  transactionIndex: Number,
  
  // Address information
  from: {
    type: String,
    required: true
  },
  
  to: String,
  
  // Gas information
  gasUsed: String,
  gasPrice: String,
  gasLimit: String,
  
  // Transaction value
  value: {
    type: String,
    default: '0'
  },
  
  // Fee information
  fee: {
    wei: String,
    matic: String
  },
  
  // Confirmations
  confirmations: {
    type: Number,
    default: 0
  },
  
  // Related artworks (for minting transactions)
  artworks: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Artwork'
  }],
  
  // NFT information (for minting transactions)
  nftData: {
    tokenIds: [String],
    contractAddress: String,
    recipient: String,
    creator: String
  },
  
  // Transaction metadata
  metadata: {
    estimatedGas: String,
    actualGas: String,
    gasEfficiency: Number, // actualGas / estimatedGas
    mintingFee: String,
    totalCost: String
  },
  
  // Error information (for failed transactions)
  error: {
    code: String,
    message: String,
    reason: String
  },
  
  // Processing timestamps
  submittedAt: {
    type: Date,
    default: Date.now
  },
  
  confirmedAt: Date,
  
  // Retry information
  retryCount: {
    type: Number,
    default: 0
  },
  
  // Events emitted by the transaction
  events: [{
    name: String,
    args: mongoose.Schema.Types.Mixed,
    signature: String
  }],
  
  // Raw transaction data
  rawTransaction: {
    nonce: Number,
    data: String,
    r: String,
    s: String,
    v: String
  }
}, {
  timestamps: true
})

// Indexes
transactionSchema.index({ user: 1, createdAt: -1 })
transactionSchema.index({ type: 1, status: 1 })
transactionSchema.index({ network: 1, blockNumber: -1 })
transactionSchema.index({ status: 1, submittedAt: 1 })

// Virtual for transaction age
transactionSchema.virtual('age').get(function() {
  return Date.now() - this.submittedAt.getTime()
})

// Virtual for transaction URL on block explorer
transactionSchema.virtual('explorerUrl').get(function() {
  const baseUrls = {
    polygon: 'https://polygonscan.com/tx/',
    mumbai: 'https://mumbai.polygonscan.com/tx/',
    amoy: 'https://amoy.polygonscan.com/tx/'
  }
  
  return `${baseUrls[this.network] || baseUrls.amoy}${this.transactionHash}`
})

// Instance method to update status
transactionSchema.methods.updateStatus = function(status, additionalData = {}) {
  this.status = status
  
  if (status === 'success' || status === 'failed') {
    this.confirmedAt = new Date()
  }
  
  // Update additional fields
  Object.assign(this, additionalData)
  
  return this.save()
}

// Instance method to add event
transactionSchema.methods.addEvent = function(name, args, signature) {
  this.events.push({ name, args, signature })
  return this.save()
}

// Instance method to calculate gas efficiency
transactionSchema.methods.calculateGasEfficiency = function() {
  if (this.metadata.estimatedGas && this.metadata.actualGas) {
    const estimated = BigInt(this.metadata.estimatedGas)
    const actual = BigInt(this.metadata.actualGas)
    this.metadata.gasEfficiency = Number(actual * BigInt(100) / estimated) / 100
  }
  return this.metadata.gasEfficiency
}

// Static method to find by user
transactionSchema.statics.findByUser = function(userId, options = {}) {
  const {
    type,
    status,
    network,
    page = 1,
    limit = 20,
    sort = '-createdAt'
  } = options
  
  const query = { user: userId }
  
  if (type) query.type = type
  if (status) query.status = status
  if (network) query.network = network
  
  return this.find(query)
    .sort(sort)
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('user', 'username email')
    .populate('artworks', 'title category')
}

// Static method to find pending transactions
transactionSchema.statics.findPending = function(maxAge = 24 * 60 * 60 * 1000) { // 24 hours
  const cutoffTime = new Date(Date.now() - maxAge)
  
  return this.find({
    status: 'pending',
    submittedAt: { $gte: cutoffTime }
  })
}

// Static method to get transaction statistics
transactionSchema.statics.getStats = function(userId, timeframe = 30) {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000)
  
  return this.aggregate([
    {
      $match: {
        user: mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        totalTransactions: { $sum: 1 },
        successfulTransactions: {
          $sum: { $cond: [{ $eq: ['$status', 'success'] }, 1, 0] }
        },
        failedTransactions: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        pendingTransactions: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        totalGasUsed: { $sum: { $toDouble: '$gasUsed' } },
        totalFees: { $sum: { $toDouble: '$fee.wei' } },
        byType: {
          $push: {
            type: '$type',
            status: '$status'
          }
        }
      }
    }
  ])
}

// Pre-save middleware to calculate fee
transactionSchema.pre('save', function(next) {
  if (this.gasUsed && this.gasPrice) {
    const feeWei = (BigInt(this.gasUsed) * BigInt(this.gasPrice)).toString()
    this.fee = {
      wei: feeWei,
      matic: (Number(feeWei) / 1e18).toString()
    }
  }
  next()
})

// Remove sensitive data when converting to JSON
transactionSchema.methods.toJSON = function() {
  const transaction = this.toObject({ virtuals: true })
  
  // Add computed fields
  transaction.explorerUrl = this.explorerUrl
  transaction.age = this.age
  
  return transaction
}

module.exports = mongoose.model('Transaction', transactionSchema)
