const mongoose = require('mongoose')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const { ethers } = require('ethers')
const encryptionService = require('../utils/encryption')

const userSchema = new mongoose.Schema({
  // Basic user information
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30,
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
  },
  
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  
  // Profile information
  profile: {
    firstName: {
      type: String,
      trim: true,
      maxlength: 50
    },
    lastName: {
      type: String,
      trim: true,
      maxlength: 50
    },
    avatar: {
      type: String, // URL to avatar image
      default: null
    },
    bio: {
      type: String,
      maxlength: 500
    }
  },
  
  // Wallet information (encrypted)
  wallet: {
    address: {
      type: String,
      unique: true,
      sparse: true // Allow null values but enforce uniqueness when present
    },
    // Encrypted private key
    encryptedPrivateKey: {
      encrypted: String,
      iv: String,
      tag: String
    }
  },
  
  // Account status
  isActive: {
    type: Boolean,
    default: true
  },
  
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  
  // Security
  lastLogin: {
    type: Date,
    default: null
  },
  
  loginAttempts: {
    type: Number,
    default: 0
  },
  
  lockUntil: {
    type: Date,
    default: null
  },
  
  // NFT statistics
  stats: {
    totalMinted: {
      type: Number,
      default: 0
    },
    totalSpent: {
      type: String, // Store as string to handle big numbers
      default: '0'
    }
  },
  
  // Refresh tokens for JWT
  refreshTokens: [{
    token: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
})

// Indexes
userSchema.index({ email: 1 })
userSchema.index({ username: 1 })
userSchema.index({ 'wallet.address': 1 })
userSchema.index({ createdAt: -1 })

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now())
})

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next()
  
  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error)
  }
})

// Pre-save middleware to generate wallet if not exists
userSchema.pre('save', async function(next) {
  if (!this.wallet || !this.wallet.address) {
    try {
      // Generate new wallet
      const wallet = ethers.Wallet.createRandom()

      // Encrypt private key
      const encryptedPrivateKey = encryptionService.encrypt(wallet.privateKey)

      // Set wallet information
      this.wallet = {
        address: wallet.address,
        encryptedPrivateKey
      }

      next()
    } catch (error) {
      next(error)
    }
  } else {
    next()
  }
})

// Instance method to check password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password)
}

// Instance method to generate JWT token
userSchema.methods.generateAuthToken = function() {
  const payload = {
    userId: this._id,
    email: this.email,
    username: this.username,
    walletAddress: this.wallet.address
  }
  
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  })
}

// Instance method to generate refresh token
userSchema.methods.generateRefreshToken = function() {
  const refreshToken = jwt.sign(
    { userId: this._id },
    process.env.JWT_SECRET + 'refresh',
    { expiresIn: '7d' }
  )
  
  // Add to refresh tokens array
  this.refreshTokens.push({
    token: refreshToken,
    createdAt: new Date()
  })
  
  // Keep only last 5 refresh tokens
  if (this.refreshTokens.length > 5) {
    this.refreshTokens = this.refreshTokens.slice(-5)
  }
  
  return refreshToken
}

// Instance method to get decrypted private key
userSchema.methods.getPrivateKey = function() {
  try {
    return encryptionService.decrypt(this.wallet.encryptedPrivateKey)
  } catch (error) {
    throw new Error('Failed to decrypt private key')
  }
}

// Instance method to get wallet instance
userSchema.methods.getWallet = function() {
  try {
    const privateKey = this.getPrivateKey()
    return new ethers.Wallet(privateKey)
  } catch (error) {
    throw new Error('Failed to create wallet instance')
  }
}

// Instance method to handle failed login attempt
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    })
  }
  
  const updates = { $inc: { loginAttempts: 1 } }
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 } // 2 hours
  }
  
  return this.updateOne(updates)
}

// Instance method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  })
}

// Static method to find by credentials
userSchema.statics.findByCredentials = async function(email, password) {
  const user = await this.findOne({ email, isActive: true })
  
  if (!user) {
    throw new Error('Invalid login credentials')
  }
  
  if (user.isLocked) {
    throw new Error('Account is temporarily locked due to too many failed login attempts')
  }
  
  const isMatch = await user.comparePassword(password)
  
  if (!isMatch) {
    await user.incLoginAttempts()
    throw new Error('Invalid login credentials')
  }
  
  // Reset login attempts on successful login
  if (user.loginAttempts > 0) {
    await user.resetLoginAttempts()
  }
  
  // Update last login
  user.lastLogin = new Date()
  await user.save()
  
  return user
}

// Static method to cleanup expired refresh tokens
userSchema.statics.cleanupExpiredTokens = async function() {
  const expiredDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
  
  return this.updateMany(
    {},
    {
      $pull: {
        refreshTokens: {
          createdAt: { $lt: expiredDate }
        }
      }
    }
  )
}

// Remove sensitive data when converting to JSON
userSchema.methods.toJSON = function() {
  const user = this.toObject()
  
  delete user.password
  delete user.wallet.encryptedPrivateKey
  delete user.refreshTokens
  delete user.loginAttempts
  delete user.lockUntil
  
  return user
}

module.exports = mongoose.model('User', userSchema)
