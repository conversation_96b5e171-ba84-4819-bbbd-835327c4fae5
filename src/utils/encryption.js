const crypto = require('crypto')

const ALGORITHM = 'aes-256-gcm'
const KEY_LENGTH = 32
const IV_LENGTH = 16
const TAG_LENGTH = 16

class EncryptionService {
  constructor() {
    this.key = this.getEncryptionKey()
  }

  getEncryptionKey() {
    const key = process.env.ENCRYPTION_KEY
    if (!key || key.length !== KEY_LENGTH) {
      throw new Error('ENCRYPTION_KEY must be exactly 32 characters long')
    }
    return Buffer.from(key, 'utf8')
  }

  encrypt(text) {
    try {
      const iv = crypto.randomBytes(IV_LENGTH)
      const cipher = crypto.createCipher(ALGORITHM, this.key)
      cipher.setAAD(Buffer.from('spray-nft', 'utf8'))
      
      let encrypted = cipher.update(text, 'utf8', 'hex')
      encrypted += cipher.final('hex')
      
      const tag = cipher.getAuthTag()
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      }
    } catch (error) {
      throw new Error(`Encryption failed: ${error.message}`)
    }
  }

  decrypt(encryptedData) {
    try {
      const { encrypted, iv, tag } = encryptedData
      const decipher = crypto.createDecipher(ALGORITHM, this.key)
      
      decipher.setAAD(Buffer.from('spray-nft', 'utf8'))
      decipher.setAuthTag(Buffer.from(tag, 'hex'))
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
      
      return decrypted
    } catch (error) {
      throw new Error(`Decryption failed: ${error.message}`)
    }
  }

  // Generate a secure random private key for wallet creation
  generatePrivateKey() {
    return crypto.randomBytes(32).toString('hex')
  }

  // Hash password for storage
  hashPassword(password) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  // Verify password
  verifyPassword(password, salt, hash) {
    const hashVerify = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex')
    return hash === hashVerify
  }
}

module.exports = new EncryptionService()
