const { ethers } = require('ethers')
const fs = require('fs')
const path = require('path')
const logger = require('../utils/logger')

class BlockchainService {
  constructor() {
    this.providers = {}
    this.contracts = {}
    this.initializeProviders()
  }

  /**
   * Initialize blockchain providers
   */
  initializeProviders() {
    try {
      // Polygon Mainnet
      this.providers.polygon = new ethers.JsonRpcProvider(
        process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com'
      )

      // Polygon Testnet (Amoy)
      this.providers.amoy = new ethers.JsonRpcProvider(
        process.env.MUMBAI_RPC_URL || 'https://rpc.ankr.com/polygon_amoy'
      )

      // Default to testnet in development
      this.currentNetwork = process.env.NODE_ENV === 'production' ? 'polygon' : 'amoy'
      this.provider = this.providers[this.currentNetwork]

      logger.info(`Blockchain service initialized for ${this.currentNetwork} network`)
    } catch (error) {
      logger.error('Failed to initialize blockchain providers:', error)
    }
  }

  /**
   * Get current provider
   */
  getProvider(network = null) {
    const targetNetwork = network || this.currentNetwork
    return this.providers[targetNetwork]
  }

  /**
   * Get network information
   */
  async getNetworkInfo(network = null) {
    try {
      const provider = this.getProvider(network)
      const networkInfo = await provider.getNetwork()
      const blockNumber = await provider.getBlockNumber()
      const feeData = await provider.getFeeData()

      return {
        name: networkInfo.name,
        chainId: networkInfo.chainId.toString(),
        blockNumber,
        gasPrice: feeData.gasPrice?.toString(),
        maxFeePerGas: feeData.maxFeePerGas?.toString(),
        maxPriorityFeePerGas: feeData.maxPriorityFeePerGas?.toString()
      }
    } catch (error) {
      logger.error('Failed to get network info:', error)
      throw new Error(`Failed to get network info: ${error.message}`)
    }
  }

  /**
   * Load smart contract
   */
  async loadContract(network = null) {
    try {
      const targetNetwork = network || this.currentNetwork
      
      // Get contract address from environment
      const contractAddress = targetNetwork === 'polygon' 
        ? process.env.NFT_CONTRACT_ADDRESS_POLYGON
        : process.env.NFT_CONTRACT_ADDRESS_MUMBAI

      if (!contractAddress) {
        throw new Error(`Contract address not configured for ${targetNetwork}`)
      }

      // Load contract ABI
      const artifactPath = path.join(__dirname, '../../artifacts/contracts/SprayNFT.sol/SprayNFT.json')
      
      if (!fs.existsSync(artifactPath)) {
        throw new Error('Contract artifact not found. Please compile the contract first.')
      }

      const artifact = JSON.parse(fs.readFileSync(artifactPath, 'utf8'))
      const provider = this.getProvider(targetNetwork)

      // Create contract instance
      const contract = new ethers.Contract(contractAddress, artifact.abi, provider)
      
      // Cache the contract
      this.contracts[targetNetwork] = contract

      logger.info(`Contract loaded for ${targetNetwork}: ${contractAddress}`)
      return contract
    } catch (error) {
      logger.error('Failed to load contract:', error)
      throw new Error(`Failed to load contract: ${error.message}`)
    }
  }

  /**
   * Get contract instance
   */
  async getContract(network = null) {
    const targetNetwork = network || this.currentNetwork
    
    if (!this.contracts[targetNetwork]) {
      await this.loadContract(targetNetwork)
    }
    
    return this.contracts[targetNetwork]
  }

  /**
   * Create wallet instance from user
   */
  createWallet(user, network = null) {
    try {
      const provider = this.getProvider(network)
      const privateKey = user.getPrivateKey()
      return new ethers.Wallet(privateKey, provider)
    } catch (error) {
      logger.error('Failed to create wallet:', error)
      throw new Error(`Failed to create wallet: ${error.message}`)
    }
  }

  /**
   * Get wallet balance
   */
  async getBalance(address, network = null) {
    try {
      const provider = this.getProvider(network)
      const balance = await provider.getBalance(address)
      
      return {
        wei: balance.toString(),
        matic: ethers.formatEther(balance)
      }
    } catch (error) {
      logger.error('Failed to get balance:', error)
      throw new Error(`Failed to get balance: ${error.message}`)
    }
  }

  /**
   * Estimate gas for contract function
   */
  async estimateGas(user, functionName, args = [], network = null) {
    try {
      const contract = await this.getContract(network)
      const wallet = this.createWallet(user, network)
      const contractWithSigner = contract.connect(wallet)

      // Get the function
      const contractFunction = contractWithSigner[functionName]
      if (!contractFunction) {
        throw new Error(`Function ${functionName} not found in contract`)
      }

      // Estimate gas
      const gasEstimate = await contractFunction.estimateGas(...args)
      
      // Get current gas price
      const feeData = await wallet.provider.getFeeData()
      
      // Calculate total cost (add 20% buffer)
      const gasLimit = gasEstimate * BigInt(120) / BigInt(100)
      const totalCost = gasLimit * feeData.gasPrice

      return {
        gasEstimate: gasEstimate.toString(),
        gasLimit: gasLimit.toString(),
        gasPrice: feeData.gasPrice.toString(),
        maxFeePerGas: feeData.maxFeePerGas?.toString(),
        maxPriorityFeePerGas: feeData.maxPriorityFeePerGas?.toString(),
        totalCost: {
          wei: totalCost.toString(),
          matic: ethers.formatEther(totalCost)
        }
      }
    } catch (error) {
      logger.error('Gas estimation failed:', error)
      throw new Error(`Gas estimation failed: ${error.message}`)
    }
  }

  /**
   * Mint NFT
   */
  async mintNFT(user, to, tokenURI, creator, options = {}) {
    try {
      const {
        network = null,
        gasLimit = null,
        gasPrice = null
      } = options

      const contract = await this.getContract(network)
      const wallet = this.createWallet(user, network)
      const contractWithSigner = contract.connect(wallet)

      // Get minting fee
      const mintingFee = await contract.mintingFee()

      // Prepare transaction options
      const txOptions = {
        value: mintingFee
      }

      if (gasLimit) txOptions.gasLimit = gasLimit
      if (gasPrice) txOptions.gasPrice = ethers.parseUnits(gasPrice.toString(), 'gwei')

      // Call mint function
      const tx = await contractWithSigner.mint(to, tokenURI, creator, txOptions)

      logger.info(`NFT mint transaction submitted: ${tx.hash}`)

      return {
        transactionHash: tx.hash,
        from: tx.from,
        to: tx.to,
        value: ethers.formatEther(tx.value || 0),
        gasLimit: tx.gasLimit?.toString(),
        gasPrice: tx.gasPrice?.toString(),
        nonce: tx.nonce,
        blockNumber: null, // Will be set after confirmation
        status: 'pending'
      }
    } catch (error) {
      logger.error('NFT minting failed:', error)
      throw new Error(`NFT minting failed: ${error.message}`)
    }
  }

  /**
   * Batch mint NFTs
   */
  async batchMintNFT(user, to, tokenURIs, creator, options = {}) {
    try {
      const {
        network = null,
        gasLimit = null,
        gasPrice = null
      } = options

      const contract = await this.getContract(network)
      const wallet = this.createWallet(user, network)
      const contractWithSigner = contract.connect(wallet)

      // Get minting fee
      const mintingFee = await contract.mintingFee()
      const totalFee = mintingFee * BigInt(tokenURIs.length)

      // Prepare transaction options
      const txOptions = {
        value: totalFee
      }

      if (gasLimit) txOptions.gasLimit = gasLimit
      if (gasPrice) txOptions.gasPrice = ethers.parseUnits(gasPrice.toString(), 'gwei')

      // Call batch mint function
      const tx = await contractWithSigner.batchMint(to, tokenURIs, creator, txOptions)

      logger.info(`Batch NFT mint transaction submitted: ${tx.hash}`)

      return {
        transactionHash: tx.hash,
        from: tx.from,
        to: tx.to,
        value: ethers.formatEther(tx.value || 0),
        gasLimit: tx.gasLimit?.toString(),
        gasPrice: tx.gasPrice?.toString(),
        nonce: tx.nonce,
        blockNumber: null,
        status: 'pending',
        tokenCount: tokenURIs.length
      }
    } catch (error) {
      logger.error('Batch NFT minting failed:', error)
      throw new Error(`Batch NFT minting failed: ${error.message}`)
    }
  }

  /**
   * Get transaction receipt
   */
  async getTransactionReceipt(transactionHash, network = null) {
    try {
      const provider = this.getProvider(network)
      
      // Get transaction receipt
      const receipt = await provider.getTransactionReceipt(transactionHash)
      
      if (!receipt) {
        return {
          status: 'pending',
          message: 'Transaction not yet mined'
        }
      }

      // Get transaction details
      const transaction = await provider.getTransaction(transactionHash)
      const currentBlock = await provider.getBlockNumber()

      return {
        transactionHash: receipt.hash,
        blockNumber: receipt.blockNumber,
        blockHash: receipt.blockHash,
        transactionIndex: receipt.index,
        from: receipt.from,
        to: receipt.to,
        gasUsed: receipt.gasUsed.toString(),
        gasPrice: transaction.gasPrice?.toString(),
        status: receipt.status === 1 ? 'success' : 'failed',
        confirmations: currentBlock - receipt.blockNumber,
        logs: receipt.logs,
        events: this.parseContractEvents(receipt.logs)
      }
    } catch (error) {
      logger.error('Failed to get transaction receipt:', error)
      throw new Error(`Failed to get transaction receipt: ${error.message}`)
    }
  }

  /**
   * Parse contract events from logs
   */
  parseContractEvents(logs) {
    try {
      const events = []
      
      for (const log of logs) {
        try {
          // Try to parse with contract interface
          const contract = this.contracts[this.currentNetwork]
          if (contract) {
            const parsedLog = contract.interface.parseLog(log)
            if (parsedLog) {
              events.push({
                name: parsedLog.name,
                args: parsedLog.args,
                signature: parsedLog.signature
              })
            }
          }
        } catch (parseError) {
          // Skip unparseable logs
          continue
        }
      }
      
      return events
    } catch (error) {
      logger.error('Failed to parse contract events:', error)
      return []
    }
  }

  /**
   * Get contract information
   */
  async getContractInfo(network = null) {
    try {
      const contract = await this.getContract(network)
      
      const [name, symbol, totalSupply, mintingFee, maxSupply, owner] = await Promise.all([
        contract.name(),
        contract.symbol(),
        contract.totalSupply(),
        contract.mintingFee(),
        contract.maxSupply(),
        contract.owner()
      ])

      return {
        address: await contract.getAddress(),
        name,
        symbol,
        totalSupply: totalSupply.toString(),
        mintingFee: {
          wei: mintingFee.toString(),
          matic: ethers.formatEther(mintingFee)
        },
        maxSupply: maxSupply.toString(),
        owner,
        network: network || this.currentNetwork
      }
    } catch (error) {
      logger.error('Failed to get contract info:', error)
      throw new Error(`Failed to get contract info: ${error.message}`)
    }
  }

  /**
   * Get user's NFTs
   */
  async getUserNFTs(userAddress, network = null) {
    try {
      const contract = await this.getContract(network)
      
      // Get tokens owned by user
      const tokenIds = await contract.tokensOfOwner(userAddress)
      
      const nfts = []
      for (const tokenId of tokenIds) {
        try {
          const tokenURI = await contract.tokenURI(tokenId)
          const creator = await contract.tokenCreators(tokenId)
          
          nfts.push({
            tokenId: tokenId.toString(),
            tokenURI,
            creator,
            owner: userAddress
          })
        } catch (error) {
          logger.warn(`Failed to get info for token ${tokenId}:`, error)
        }
      }

      return nfts
    } catch (error) {
      logger.error('Failed to get user NFTs:', error)
      throw new Error(`Failed to get user NFTs: ${error.message}`)
    }
  }

  /**
   * Monitor transaction
   */
  async monitorTransaction(transactionHash, network = null, maxWaitTime = 300000) {
    return new Promise((resolve, reject) => {
      const provider = this.getProvider(network)
      const startTime = Date.now()

      const checkTransaction = async () => {
        try {
          const receipt = await this.getTransactionReceipt(transactionHash, network)
          
          if (receipt.status !== 'pending') {
            resolve(receipt)
            return
          }

          // Check timeout
          if (Date.now() - startTime > maxWaitTime) {
            reject(new Error('Transaction monitoring timeout'))
            return
          }

          // Check again in 5 seconds
          setTimeout(checkTransaction, 5000)
        } catch (error) {
          reject(error)
        }
      }

      checkTransaction()
    })
  }
}

module.exports = new BlockchainService()
