const axios = require('axios')
const FormData = require('form-data')
const fs = require('fs')
const path = require('path')
const logger = require('../utils/logger')

class IPFSService {
  constructor() {
    this.pinataApiKey = process.env.PINATA_API_KEY
    this.pinataSecretKey = process.env.PINATA_SECRET_API_KEY
    this.pinataJWT = process.env.PINATA_JWT
    this.pinataBaseUrl = 'https://api.pinata.cloud'
    this.gatewayUrl = 'https://gateway.pinata.cloud/ipfs/'
    
    // Validate configuration
    if (!this.pinataApiKey || !this.pinataSecretKey) {
      logger.warn('Pinata API credentials not configured. IPFS functionality will be limited.')
    }
  }

  /**
   * Test Pinata connection
   */
  async testConnection() {
    try {
      const response = await axios.get(`${this.pinataBaseUrl}/data/testAuthentication`, {
        headers: {
          'pinata_api_key': this.pinataApiKey,
          'pinata_secret_api_key': this.pinataSecretKey
        }
      })
      
      logger.info('Pinata connection test successful')
      return { success: true, message: response.data.message }
    } catch (error) {
      logger.error('Pinata connection test failed:', error.response?.data || error.message)
      return { success: false, error: error.response?.data || error.message }
    }
  }

  /**
   * Upload file to IPFS via Pinata
   */
  async uploadFile(filePath, options = {}) {
    try {
      const {
        pinataMetadata = {},
        pinataOptions = {}
      } = options

      // Create form data
      const formData = new FormData()
      formData.append('file', fs.createReadStream(filePath))
      
      // Add metadata
      if (Object.keys(pinataMetadata).length > 0) {
        formData.append('pinataMetadata', JSON.stringify(pinataMetadata))
      }
      
      // Add options
      if (Object.keys(pinataOptions).length > 0) {
        formData.append('pinataOptions', JSON.stringify(pinataOptions))
      }

      // Upload to Pinata
      const response = await axios.post(
        `${this.pinataBaseUrl}/pinning/pinFileToIPFS`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'pinata_api_key': this.pinataApiKey,
            'pinata_secret_api_key': this.pinataSecretKey
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      )

      const result = {
        ipfsHash: response.data.IpfsHash,
        pinSize: response.data.PinSize,
        timestamp: response.data.Timestamp,
        ipfsUrl: `${this.gatewayUrl}${response.data.IpfsHash}`,
        isDuplicate: response.data.isDuplicate || false
      }

      logger.info(`File uploaded to IPFS: ${result.ipfsHash}`)
      return result
    } catch (error) {
      logger.error('IPFS file upload failed:', error.response?.data || error.message)
      throw new Error(`IPFS upload failed: ${error.response?.data?.error || error.message}`)
    }
  }

  /**
   * Upload JSON data to IPFS via Pinata
   */
  async uploadJSON(jsonData, options = {}) {
    try {
      const {
        pinataMetadata = {},
        pinataOptions = {}
      } = options

      const data = {
        pinataContent: jsonData,
        pinataMetadata,
        pinataOptions
      }

      const response = await axios.post(
        `${this.pinataBaseUrl}/pinning/pinJSONToIPFS`,
        data,
        {
          headers: {
            'Content-Type': 'application/json',
            'pinata_api_key': this.pinataApiKey,
            'pinata_secret_api_key': this.pinataSecretKey
          }
        }
      )

      const result = {
        ipfsHash: response.data.IpfsHash,
        pinSize: response.data.PinSize,
        timestamp: response.data.Timestamp,
        ipfsUrl: `${this.gatewayUrl}${response.data.IpfsHash}`,
        isDuplicate: response.data.isDuplicate || false
      }

      logger.info(`JSON uploaded to IPFS: ${result.ipfsHash}`)
      return result
    } catch (error) {
      logger.error('IPFS JSON upload failed:', error.response?.data || error.message)
      throw new Error(`IPFS JSON upload failed: ${error.response?.data?.error || error.message}`)
    }
  }

  /**
   * Upload file from buffer
   */
  async uploadBuffer(buffer, fileName, mimeType, options = {}) {
    try {
      const {
        pinataMetadata = {},
        pinataOptions = {}
      } = options

      const formData = new FormData()
      formData.append('file', buffer, {
        filename: fileName,
        contentType: mimeType
      })
      
      if (Object.keys(pinataMetadata).length > 0) {
        formData.append('pinataMetadata', JSON.stringify(pinataMetadata))
      }
      
      if (Object.keys(pinataOptions).length > 0) {
        formData.append('pinataOptions', JSON.stringify(pinataOptions))
      }

      const response = await axios.post(
        `${this.pinataBaseUrl}/pinning/pinFileToIPFS`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'pinata_api_key': this.pinataApiKey,
            'pinata_secret_api_key': this.pinataSecretKey
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      )

      const result = {
        ipfsHash: response.data.IpfsHash,
        pinSize: response.data.PinSize,
        timestamp: response.data.Timestamp,
        ipfsUrl: `${this.gatewayUrl}${response.data.IpfsHash}`,
        isDuplicate: response.data.isDuplicate || false
      }

      logger.info(`Buffer uploaded to IPFS: ${result.ipfsHash}`)
      return result
    } catch (error) {
      logger.error('IPFS buffer upload failed:', error.response?.data || error.message)
      throw new Error(`IPFS buffer upload failed: ${error.response?.data?.error || error.message}`)
    }
  }

  /**
   * Get pinned files list
   */
  async getPinnedFiles(options = {}) {
    try {
      const {
        hashContains,
        pinStart,
        pinEnd,
        unpinStart,
        unpinEnd,
        pinSizeMin,
        pinSizeMax,
        pageLimit = 10,
        pageOffset = 0
      } = options

      const params = new URLSearchParams({
        pageLimit: pageLimit.toString(),
        pageOffset: pageOffset.toString()
      })

      if (hashContains) params.append('hashContains', hashContains)
      if (pinStart) params.append('pinStart', pinStart)
      if (pinEnd) params.append('pinEnd', pinEnd)
      if (unpinStart) params.append('unpinStart', unpinStart)
      if (unpinEnd) params.append('unpinEnd', unpinEnd)
      if (pinSizeMin) params.append('pinSizeMin', pinSizeMin.toString())
      if (pinSizeMax) params.append('pinSizeMax', pinSizeMax.toString())

      const response = await axios.get(
        `${this.pinataBaseUrl}/data/pinList?${params}`,
        {
          headers: {
            'pinata_api_key': this.pinataApiKey,
            'pinata_secret_api_key': this.pinataSecretKey
          }
        }
      )

      return response.data
    } catch (error) {
      logger.error('Failed to get pinned files:', error.response?.data || error.message)
      throw new Error(`Failed to get pinned files: ${error.response?.data?.error || error.message}`)
    }
  }

  /**
   * Unpin file from IPFS
   */
  async unpinFile(ipfsHash) {
    try {
      await axios.delete(
        `${this.pinataBaseUrl}/pinning/unpin/${ipfsHash}`,
        {
          headers: {
            'pinata_api_key': this.pinataApiKey,
            'pinata_secret_api_key': this.pinataSecretKey
          }
        }
      )

      logger.info(`File unpinned from IPFS: ${ipfsHash}`)
      return { success: true, message: 'File unpinned successfully' }
    } catch (error) {
      logger.error('Failed to unpin file:', error.response?.data || error.message)
      throw new Error(`Failed to unpin file: ${error.response?.data?.error || error.message}`)
    }
  }

  /**
   * Get file content from IPFS
   */
  async getFileContent(ipfsHash) {
    try {
      const response = await axios.get(`${this.gatewayUrl}${ipfsHash}`, {
        timeout: 30000 // 30 seconds timeout
      })

      return response.data
    } catch (error) {
      logger.error('Failed to get file content:', error.message)
      throw new Error(`Failed to get file content: ${error.message}`)
    }
  }

  /**
   * Generate NFT metadata according to ERC-721 standard
   */
  generateNFTMetadata(artwork, options = {}) {
    const {
      externalUrl,
      backgroundColor,
      animationUrl
    } = options

    const metadata = {
      name: artwork.title,
      description: artwork.description || '',
      image: artwork.files.main?.ipfsUrl || artwork.files.thumbnail?.ipfsUrl,
      external_url: externalUrl || '',
      attributes: []
    }

    // Add artwork attributes
    if (artwork.category) {
      metadata.attributes.push({
        trait_type: 'Category',
        value: artwork.category
      })
    }

    if (artwork.attributes?.style) {
      metadata.attributes.push({
        trait_type: 'Style',
        value: artwork.attributes.style
      })
    }

    if (artwork.attributes?.medium) {
      metadata.attributes.push({
        trait_type: 'Medium',
        value: artwork.attributes.medium
      })
    }

    if (artwork.attributes?.complexity) {
      metadata.attributes.push({
        trait_type: 'Complexity',
        value: artwork.attributes.complexity
      })
    }

    // Add dimensions if available
    if (artwork.attributes?.dimensions) {
      const { width, height, depth } = artwork.attributes.dimensions
      if (width) {
        metadata.attributes.push({
          trait_type: 'Width',
          value: width,
          display_type: 'number'
        })
      }
      if (height) {
        metadata.attributes.push({
          trait_type: 'Height',
          value: height,
          display_type: 'number'
        })
      }
      if (depth) {
        metadata.attributes.push({
          trait_type: 'Depth',
          value: depth,
          display_type: 'number'
        })
      }
    }

    // Add Unity AR specific attributes
    if (artwork.unityData?.objectCount) {
      metadata.attributes.push({
        trait_type: 'Object Count',
        value: artwork.unityData.objectCount,
        display_type: 'number'
      })
    }

    // Add tags as attributes
    if (artwork.attributes?.tags && artwork.attributes.tags.length > 0) {
      artwork.attributes.tags.forEach(tag => {
        metadata.attributes.push({
          trait_type: 'Tag',
          value: tag
        })
      })
    }

    // Add optional fields
    if (backgroundColor) metadata.background_color = backgroundColor
    if (animationUrl) metadata.animation_url = animationUrl

    // Add creation date
    metadata.attributes.push({
      trait_type: 'Created',
      value: artwork.createdAt.toISOString().split('T')[0],
      display_type: 'date'
    })

    return metadata
  }

  /**
   * Get IPFS gateway URL
   */
  getGatewayUrl(ipfsHash) {
    return `${this.gatewayUrl}${ipfsHash}`
  }
}

module.exports = new IPFSService()
