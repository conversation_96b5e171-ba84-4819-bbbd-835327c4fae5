const jwt = require('jsonwebtoken')
const User = require('../models/User')
const logger = require('../utils/logger')

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
const auth = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization')
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      })
    }
    
    // Extract token from "Bearer <token>"
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid token format.'
      })
    }
    
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    
    // Find user
    const user = await User.findById(decoded.userId).select('-password -refreshTokens')
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. User not found.'
      })
    }
    
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Account is deactivated.'
      })
    }
    
    if (user.isLocked) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Account is temporarily locked.'
      })
    }
    
    // Attach user to request
    req.user = user
    req.token = token
    
    next()
  } catch (error) {
    logger.error('Authentication error:', error)
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid token.'
      })
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Token expired.'
      })
    }
    
    res.status(500).json({
      success: false,
      message: 'Internal server error during authentication.'
    })
  }
}

/**
 * Optional authentication middleware
 * Attaches user to request if token is valid, but doesn't require it
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization')
    
    if (!authHeader) {
      return next()
    }
    
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader
    
    if (!token) {
      return next()
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    const user = await User.findById(decoded.userId).select('-password -refreshTokens')
    
    if (user && user.isActive && !user.isLocked) {
      req.user = user
      req.token = token
    }
    
    next()
  } catch (error) {
    // Ignore authentication errors in optional auth
    next()
  }
}

/**
 * Admin authentication middleware
 * Requires user to be authenticated and have admin role
 */
const adminAuth = async (req, res, next) => {
  try {
    // First run regular auth
    await new Promise((resolve, reject) => {
      auth(req, res, (err) => {
        if (err) reject(err)
        else resolve()
      })
    })
    
    // Check if user has admin role (you can extend User model to include roles)
    // For now, we'll check if user is the contract owner or has admin email
    const adminEmails = (process.env.ADMIN_EMAILS || '').split(',').map(email => email.trim())
    
    if (!adminEmails.includes(req.user.email)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      })
    }
    
    next()
  } catch (error) {
    logger.error('Admin authentication error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error during admin authentication.'
    })
  }
}

/**
 * Rate limiting middleware for authentication endpoints
 */
const authRateLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
  const attempts = new Map()
  
  return (req, res, next) => {
    const key = req.ip + ':' + (req.body.email || req.body.username || 'unknown')
    const now = Date.now()
    
    // Clean up old entries
    for (const [k, v] of attempts.entries()) {
      if (now - v.timestamp > windowMs) {
        attempts.delete(k)
      }
    }
    
    const userAttempts = attempts.get(key)
    
    if (userAttempts && userAttempts.count >= maxAttempts) {
      const timeLeft = Math.ceil((userAttempts.timestamp + windowMs - now) / 1000 / 60)
      return res.status(429).json({
        success: false,
        message: `Too many authentication attempts. Try again in ${timeLeft} minutes.`
      })
    }
    
    // Track this attempt
    attempts.set(key, {
      count: userAttempts ? userAttempts.count + 1 : 1,
      timestamp: userAttempts ? userAttempts.timestamp : now
    })
    
    next()
  }
}

/**
 * Middleware to validate refresh token
 */
const validateRefreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body
    
    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token is required.'
      })
    }
    
    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET + 'refresh')
    
    // Find user and check if refresh token exists
    const user = await User.findById(decoded.userId)
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token.'
      })
    }
    
    const tokenExists = user.refreshTokens.some(t => t.token === refreshToken)
    
    if (!tokenExists) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token.'
      })
    }
    
    req.user = user
    req.refreshToken = refreshToken
    
    next()
  } catch (error) {
    logger.error('Refresh token validation error:', error)
    
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token.'
      })
    }
    
    res.status(500).json({
      success: false,
      message: 'Internal server error during token validation.'
    })
  }
}

module.exports = {
  auth,
  optionalAuth,
  adminAuth,
  authRateLimit,
  validateRefreshToken
}
