const logger = require('../utils/logger')
const os = require('os')
const process = require('process')

/**
 * System monitoring and health check middleware
 */

// System metrics collection
class SystemMonitor {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        averageResponseTime: 0
      },
      system: {
        uptime: 0,
        memory: {},
        cpu: {},
        disk: {}
      },
      database: {
        connections: 0,
        queries: 0,
        errors: 0
      },
      blockchain: {
        transactions: 0,
        successful: 0,
        failed: 0,
        gasUsed: 0
      }
    }
    
    this.responseTimes = []
    this.startTime = Date.now()
    
    // Start periodic system monitoring
    this.startSystemMonitoring()
  }

  // Record request metrics
  recordRequest(duration, success = true) {
    this.metrics.requests.total++
    
    if (success) {
      this.metrics.requests.success++
    } else {
      this.metrics.requests.errors++
    }
    
    // Track response times (keep last 1000)
    this.responseTimes.push(duration)
    if (this.responseTimes.length > 1000) {
      this.responseTimes.shift()
    }
    
    // Calculate average response time
    this.metrics.requests.averageResponseTime = 
      this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length
  }

  // Record database metrics
  recordDatabaseQuery(success = true) {
    this.metrics.database.queries++
    if (!success) {
      this.metrics.database.errors++
    }
  }

  // Record blockchain metrics
  recordBlockchainTransaction(success = true, gasUsed = 0) {
    this.metrics.blockchain.transactions++
    
    if (success) {
      this.metrics.blockchain.successful++
    } else {
      this.metrics.blockchain.failed++
    }
    
    this.metrics.blockchain.gasUsed += gasUsed
  }

  // Get current metrics
  getMetrics() {
    this.updateSystemMetrics()
    return {
      ...this.metrics,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime
    }
  }

  // Update system metrics
  updateSystemMetrics() {
    const memUsage = process.memoryUsage()
    
    this.metrics.system = {
      uptime: process.uptime(),
      memory: {
        used: memUsage.heapUsed,
        total: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
        free: os.freemem(),
        total_system: os.totalmem()
      },
      cpu: {
        usage: process.cpuUsage(),
        load: os.loadavg(),
        cores: os.cpus().length
      },
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version
    }
  }

  // Start periodic system monitoring
  startSystemMonitoring() {
    setInterval(() => {
      const metrics = this.getMetrics()
      
      // Log system metrics every 5 minutes
      logger.info('System metrics', {
        requests: metrics.requests,
        memory: metrics.system.memory,
        uptime: metrics.uptime
      })
      
      // Check for alerts
      this.checkAlerts(metrics)
    }, 5 * 60 * 1000) // 5 minutes
  }

  // Check for system alerts
  checkAlerts(metrics) {
    const alerts = []
    
    // Memory usage alert (>80%)
    const memoryUsage = metrics.system.memory.used / metrics.system.memory.total
    if (memoryUsage > 0.8) {
      alerts.push({
        type: 'memory',
        level: 'warning',
        message: `High memory usage: ${(memoryUsage * 100).toFixed(1)}%`
      })
    }
    
    // Error rate alert (>5%)
    const errorRate = metrics.requests.errors / metrics.requests.total
    if (errorRate > 0.05 && metrics.requests.total > 100) {
      alerts.push({
        type: 'error_rate',
        level: 'critical',
        message: `High error rate: ${(errorRate * 100).toFixed(1)}%`
      })
    }
    
    // Response time alert (>2000ms)
    if (metrics.requests.averageResponseTime > 2000) {
      alerts.push({
        type: 'response_time',
        level: 'warning',
        message: `Slow response time: ${metrics.requests.averageResponseTime.toFixed(0)}ms`
      })
    }
    
    // Database error alert
    const dbErrorRate = metrics.database.errors / metrics.database.queries
    if (dbErrorRate > 0.01 && metrics.database.queries > 50) {
      alerts.push({
        type: 'database',
        level: 'critical',
        message: `High database error rate: ${(dbErrorRate * 100).toFixed(1)}%`
      })
    }
    
    // Log alerts
    alerts.forEach(alert => {
      logger.warn(`ALERT [${alert.type}]: ${alert.message}`, {
        level: alert.level,
        metrics: metrics
      })
    })
  }

  // Reset metrics
  reset() {
    this.metrics.requests = {
      total: 0,
      success: 0,
      errors: 0,
      averageResponseTime: 0
    }
    this.responseTimes = []
  }
}

// Global monitor instance
const monitor = new SystemMonitor()

// Request monitoring middleware
const requestMonitoring = (req, res, next) => {
  const startTime = Date.now()
  
  // Override res.end to capture response
  const originalEnd = res.end
  res.end = function(...args) {
    const duration = Date.now() - startTime
    const success = res.statusCode < 400
    
    // Record metrics
    monitor.recordRequest(duration, success)
    
    // Log request details
    logger.info(`${req.method} ${req.path}`, {
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user ? req.user._id : null,
      success
    })
    
    originalEnd.apply(this, args)
  }
  
  next()
}

// Health check endpoint handler
const healthCheck = (req, res) => {
  const metrics = monitor.getMetrics()
  
  // Determine health status
  const memoryUsage = metrics.system.memory.used / metrics.system.memory.total
  const errorRate = metrics.requests.errors / (metrics.requests.total || 1)
  
  let status = 'healthy'
  let issues = []
  
  if (memoryUsage > 0.9) {
    status = 'unhealthy'
    issues.push('High memory usage')
  } else if (memoryUsage > 0.8) {
    status = 'degraded'
    issues.push('Elevated memory usage')
  }
  
  if (errorRate > 0.1) {
    status = 'unhealthy'
    issues.push('High error rate')
  } else if (errorRate > 0.05) {
    status = 'degraded'
    issues.push('Elevated error rate')
  }
  
  if (metrics.requests.averageResponseTime > 5000) {
    status = 'unhealthy'
    issues.push('Very slow response times')
  } else if (metrics.requests.averageResponseTime > 2000) {
    status = 'degraded'
    issues.push('Slow response times')
  }
  
  const healthData = {
    status,
    timestamp: new Date().toISOString(),
    uptime: metrics.uptime,
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    issues,
    metrics: {
      requests: metrics.requests,
      memory: {
        usage: `${(memoryUsage * 100).toFixed(1)}%`,
        free: `${(metrics.system.memory.free / 1024 / 1024).toFixed(0)}MB`,
        total: `${(metrics.system.memory.total_system / 1024 / 1024).toFixed(0)}MB`
      },
      database: metrics.database,
      blockchain: metrics.blockchain
    }
  }
  
  const statusCode = status === 'healthy' ? 200 : status === 'degraded' ? 200 : 503
  res.status(statusCode).json(healthData)
}

// Metrics endpoint handler
const metricsEndpoint = (req, res) => {
  const metrics = monitor.getMetrics()
  res.json(metrics)
}

// Error tracking middleware
const errorTracking = (err, req, res, next) => {
  // Record error metrics
  monitor.recordRequest(0, false)
  
  // Log error details
  logger.error('Request error', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user ? req.user._id : null,
    body: req.body,
    params: req.params,
    query: req.query
  })
  
  // Don't expose sensitive information in production
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    error: isDevelopment ? {
      stack: err.stack,
      details: err
    } : undefined
  })
}

// Database monitoring wrapper
const monitorDatabase = (operation) => {
  return async (...args) => {
    try {
      const result = await operation(...args)
      monitor.recordDatabaseQuery(true)
      return result
    } catch (error) {
      monitor.recordDatabaseQuery(false)
      throw error
    }
  }
}

// Blockchain monitoring wrapper
const monitorBlockchain = (operation) => {
  return async (...args) => {
    try {
      const result = await operation(...args)
      const gasUsed = result.gasUsed ? parseInt(result.gasUsed) : 0
      monitor.recordBlockchainTransaction(true, gasUsed)
      return result
    } catch (error) {
      monitor.recordBlockchainTransaction(false, 0)
      throw error
    }
  }
}

module.exports = {
  monitor,
  requestMonitoring,
  healthCheck,
  metricsEndpoint,
  errorTracking,
  monitorDatabase,
  monitorBlockchain
}
