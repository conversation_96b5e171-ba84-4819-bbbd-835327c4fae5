const multer = require('multer')
const path = require('path')
const fs = require('fs')
const logger = require('../utils/logger')

// Ensure upload directories exist
const uploadDir = path.join(__dirname, '../../uploads')
const tempDir = path.join(uploadDir, 'temp')

if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}

if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true })
}

// File type validation
const allowedMimeTypes = {
  images: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ],
  models: [
    'model/gltf-binary',
    'application/octet-stream', // .glb files
    'model/gltf+json',
    'application/json' // .gltf files
  ],
  archives: [
    'application/zip',
    'application/x-zip-compressed'
  ]
}

const allAllowedTypes = [
  ...allowedMimeTypes.images,
  ...allowedMimeTypes.models,
  ...allowedMimeTypes.archives
]

// File size limits (in bytes)
const fileSizeLimits = {
  image: 10 * 1024 * 1024,      // 10MB for images
  model: 100 * 1024 * 1024,     // 100MB for 3D models
  archive: 200 * 1024 * 1024,   // 200MB for archives
  default: 50 * 1024 * 1024     // 50MB default
}

/**
 * Get file size limit based on mime type
 */
function getFileSizeLimit(mimeType) {
  if (allowedMimeTypes.images.includes(mimeType)) {
    return fileSizeLimits.image
  }
  if (allowedMimeTypes.models.includes(mimeType)) {
    return fileSizeLimits.model
  }
  if (allowedMimeTypes.archives.includes(mimeType)) {
    return fileSizeLimits.archive
  }
  return fileSizeLimits.default
}

/**
 * File filter function
 */
function fileFilter(req, file, cb) {
  // Check if file type is allowed
  if (!allAllowedTypes.includes(file.mimetype)) {
    const error = new Error(`File type ${file.mimetype} is not allowed`)
    error.code = 'INVALID_FILE_TYPE'
    return cb(error, false)
  }

  // Check file size limit
  const sizeLimit = getFileSizeLimit(file.mimetype)
  if (req.headers['content-length'] && parseInt(req.headers['content-length']) > sizeLimit) {
    const error = new Error(`File size exceeds limit of ${sizeLimit / 1024 / 1024}MB`)
    error.code = 'FILE_TOO_LARGE'
    return cb(error, false)
  }

  cb(null, true)
}

/**
 * Generate unique filename
 */
function generateFilename(originalname) {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  const ext = path.extname(originalname)
  const name = path.basename(originalname, ext).replace(/[^a-zA-Z0-9]/g, '_')
  return `${timestamp}_${random}_${name}${ext}`
}

/**
 * Multer storage configuration
 */
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, tempDir)
  },
  filename: function (req, file, cb) {
    const filename = generateFilename(file.originalname)
    cb(null, filename)
  }
})

/**
 * Main multer configuration
 */
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: Math.max(...Object.values(fileSizeLimits)), // Use the largest limit
    files: 10, // Maximum 10 files per request
    fields: 20 // Maximum 20 non-file fields
  }
})

/**
 * Middleware for single file upload
 */
const uploadSingle = (fieldName = 'file') => {
  return (req, res, next) => {
    const singleUpload = upload.single(fieldName)
    
    singleUpload(req, res, (err) => {
      if (err) {
        logger.error('File upload error:', err)
        
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: 'File too large',
            maxSize: `${Math.max(...Object.values(fileSizeLimits)) / 1024 / 1024}MB`
          })
        }
        
        if (err.code === 'INVALID_FILE_TYPE') {
          return res.status(400).json({
            success: false,
            message: err.message,
            allowedTypes: allAllowedTypes
          })
        }
        
        if (err.code === 'FILE_TOO_LARGE') {
          return res.status(400).json({
            success: false,
            message: err.message
          })
        }
        
        return res.status(400).json({
          success: false,
          message: 'File upload failed',
          error: err.message
        })
      }
      
      next()
    })
  }
}

/**
 * Middleware for multiple file upload
 */
const uploadMultiple = (fieldName = 'files', maxCount = 5) => {
  return (req, res, next) => {
    const multipleUpload = upload.array(fieldName, maxCount)
    
    multipleUpload(req, res, (err) => {
      if (err) {
        logger.error('Multiple file upload error:', err)
        
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: 'One or more files are too large',
            maxSize: `${Math.max(...Object.values(fileSizeLimits)) / 1024 / 1024}MB`
          })
        }
        
        if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return res.status(400).json({
            success: false,
            message: `Too many files. Maximum ${maxCount} files allowed.`
          })
        }
        
        return res.status(400).json({
          success: false,
          message: 'File upload failed',
          error: err.message
        })
      }
      
      next()
    })
  }
}

/**
 * Middleware for mixed file upload (different field names)
 */
const uploadFields = (fields) => {
  return (req, res, next) => {
    const fieldsUpload = upload.fields(fields)
    
    fieldsUpload(req, res, (err) => {
      if (err) {
        logger.error('Fields upload error:', err)
        
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: 'One or more files are too large'
          })
        }
        
        return res.status(400).json({
          success: false,
          message: 'File upload failed',
          error: err.message
        })
      }
      
      next()
    })
  }
}

/**
 * Cleanup temporary files
 */
const cleanupTempFiles = (files) => {
  if (!files) return
  
  const fileList = Array.isArray(files) ? files : [files]
  
  fileList.forEach(file => {
    if (file && file.path) {
      fs.unlink(file.path, (err) => {
        if (err) {
          logger.error('Failed to cleanup temp file:', err)
        } else {
          logger.debug(`Cleaned up temp file: ${file.path}`)
        }
      })
    }
  })
}

/**
 * Middleware to cleanup files on error
 */
const cleanupOnError = (req, res, next) => {
  const originalSend = res.send
  
  res.send = function(data) {
    // If response is an error, cleanup uploaded files
    if (res.statusCode >= 400) {
      if (req.file) cleanupTempFiles(req.file)
      if (req.files) {
        if (Array.isArray(req.files)) {
          cleanupTempFiles(req.files)
        } else {
          Object.values(req.files).forEach(files => {
            cleanupTempFiles(files)
          })
        }
      }
    }
    
    originalSend.call(this, data)
  }
  
  next()
}

/**
 * Get file type category
 */
function getFileCategory(mimeType) {
  if (allowedMimeTypes.images.includes(mimeType)) return 'image'
  if (allowedMimeTypes.models.includes(mimeType)) return 'model'
  if (allowedMimeTypes.archives.includes(mimeType)) return 'archive'
  return 'unknown'
}

/**
 * Validate file extension matches mime type
 */
function validateFileExtension(filename, mimeType) {
  const ext = path.extname(filename).toLowerCase()
  
  const mimeToExt = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'model/gltf-binary': ['.glb'],
    'application/octet-stream': ['.glb'],
    'model/gltf+json': ['.gltf'],
    'application/json': ['.gltf'],
    'application/zip': ['.zip'],
    'application/x-zip-compressed': ['.zip']
  }
  
  const allowedExts = mimeToExt[mimeType]
  return allowedExts ? allowedExts.includes(ext) : false
}

module.exports = {
  upload,
  uploadSingle,
  uploadMultiple,
  uploadFields,
  cleanupTempFiles,
  cleanupOnError,
  getFileCategory,
  validateFileExtension,
  allowedMimeTypes,
  fileSizeLimits
}
