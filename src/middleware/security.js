const rateLimit = require('express-rate-limit')
const helmet = require('helmet')
const mongoSanitize = require('express-mongo-sanitize')
const xss = require('xss-clean')
const hpp = require('hpp')
const cors = require('cors')
const logger = require('../utils/logger')

/**
 * Security middleware configuration
 */

// Rate limiting configurations
const createRateLimit = (windowMs, max, message, skipSuccessfulRequests = false) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message,
      error: 'Rate limit exceeded'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests,
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}, endpoint: ${req.path}`)
      res.status(429).json({
        success: false,
        message,
        error: 'Rate limit exceeded'
      })
    }
  })
}

// General rate limiting
const generalLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  100, // limit each IP to 100 requests per windowMs
  'Too many requests from this IP, please try again later.'
)

// Strict rate limiting for authentication endpoints
const authLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // limit each IP to 5 requests per windowMs
  'Too many authentication attempts, please try again later.',
  true // skip successful requests
)

// File upload rate limiting
const uploadLimiter = createRateLimit(
  60 * 60 * 1000, // 1 hour
  10, // limit each IP to 10 uploads per hour
  'Too many file uploads, please try again later.'
)

// NFT minting rate limiting
const mintingLimiter = createRateLimit(
  60 * 60 * 1000, // 1 hour
  20, // limit each IP to 20 minting requests per hour
  'Too many minting requests, please try again later.'
)

// Password reset rate limiting
const passwordResetLimiter = createRateLimit(
  60 * 60 * 1000, // 1 hour
  3, // limit each IP to 3 password reset requests per hour
  'Too many password reset attempts, please try again later.'
)

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true)
    
    const allowedOrigins = process.env.ALLOWED_ORIGINS 
      ? process.env.ALLOWED_ORIGINS.split(',')
      : ['http://localhost:3000', 'http://localhost:3001']
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true)
    } else {
      logger.warn(`CORS blocked request from origin: ${origin}`)
      callback(new Error('Not allowed by CORS'))
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}

// Helmet configuration for security headers
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.pinata.cloud", "https://*.infura.io"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" }
}

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Sanitize request body
  if (req.body) {
    req.body = sanitizeObject(req.body)
  }
  
  // Sanitize query parameters
  if (req.query) {
    req.query = sanitizeObject(req.query)
  }
  
  // Sanitize URL parameters
  if (req.params) {
    req.params = sanitizeObject(req.params)
  }
  
  next()
}

// Recursive object sanitization
const sanitizeObject = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return obj
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject)
  }
  
  const sanitized = {}
  for (const [key, value] of Object.entries(obj)) {
    // Skip potentially dangerous keys
    if (key.startsWith('$') || key.includes('.')) {
      continue
    }
    
    if (typeof value === 'string') {
      // Basic XSS protection
      sanitized[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    } else {
      sanitized[key] = sanitizeObject(value)
    }
  }
  
  return sanitized
}

// Request logging middleware
const requestLogger = (req, res, next) => {
  const start = Date.now()
  
  // Log request
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user ? req.user._id : null
  })
  
  // Log response
  res.on('finish', () => {
    const duration = Date.now() - start
    logger.info(`${req.method} ${req.path} - ${res.statusCode}`, {
      duration,
      ip: req.ip,
      userId: req.user ? req.user._id : null
    })
  })
  
  next()
}

// Security headers middleware
const securityHeaders = (req, res, next) => {
  // Remove server information
  res.removeHeader('X-Powered-By')
  
  // Add custom security headers
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()')
  
  next()
}

// File upload security
const fileUploadSecurity = (req, res, next) => {
  if (req.files || req.file) {
    const files = req.files || [req.file]
    
    for (const file of files) {
      if (!file) continue
      
      // Check file size
      if (file.size > 100 * 1024 * 1024) { // 100MB
        return res.status(400).json({
          success: false,
          message: 'File too large. Maximum size is 100MB.'
        })
      }
      
      // Check file type
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'model/gltf-binary', 'model/gltf+json',
        'application/zip', 'application/x-zip-compressed'
      ]
      
      if (!allowedTypes.includes(file.mimetype)) {
        return res.status(400).json({
          success: false,
          message: `File type ${file.mimetype} not allowed.`
        })
      }
      
      // Sanitize filename
      file.originalname = file.originalname
        .replace(/[^a-zA-Z0-9.-]/g, '_')
        .substring(0, 255)
    }
  }
  
  next()
}

// API key validation middleware
const validateApiKey = (req, res, next) => {
  const apiKey = req.header('X-API-Key')
  
  if (process.env.REQUIRE_API_KEY === 'true') {
    if (!apiKey || apiKey !== process.env.API_KEY) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or missing API key'
      })
    }
  }
  
  next()
}

// IP whitelist middleware
const ipWhitelist = (req, res, next) => {
  if (process.env.IP_WHITELIST) {
    const allowedIPs = process.env.IP_WHITELIST.split(',')
    const clientIP = req.ip || req.connection.remoteAddress
    
    if (!allowedIPs.includes(clientIP)) {
      logger.warn(`Blocked request from non-whitelisted IP: ${clientIP}`)
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      })
    }
  }
  
  next()
}

module.exports = {
  // Rate limiters
  generalLimiter,
  authLimiter,
  uploadLimiter,
  mintingLimiter,
  passwordResetLimiter,
  
  // Security middleware
  cors: cors(corsOptions),
  helmet: helmet(helmetConfig),
  mongoSanitize: mongoSanitize(),
  xss: xss(),
  hpp: hpp(),
  
  // Custom middleware
  sanitizeInput,
  requestLogger,
  securityHeaders,
  fileUploadSecurity,
  validateApiKey,
  ipWhitelist
}
