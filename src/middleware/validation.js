const { body, param, query } = require('express-validator')

/**
 * Validation rules for user registration
 */
const validateRegister = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('firstName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('First name must be less than 50 characters')
    .trim(),
  
  body('lastName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Last name must be less than 50 characters')
    .trim()
]

/**
 * Validation rules for user login
 */
const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
]

/**
 * Validation rules for profile update
 */
const validateProfileUpdate = [
  body('firstName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('First name must be less than 50 characters')
    .trim(),
  
  body('lastName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Last name must be less than 50 characters')
    .trim(),
  
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Bio must be less than 500 characters')
    .trim()
]

/**
 * Validation rules for password change
 */
const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match new password')
      }
      return true
    })
]

/**
 * Validation rules for account deactivation
 */
const validateAccountDeactivation = [
  body('password')
    .notEmpty()
    .withMessage('Password is required to deactivate account')
]

/**
 * Validation rules for artwork upload
 */
const validateArtworkUpload = [
  body('title')
    .notEmpty()
    .withMessage('Artwork title is required')
    .isLength({ max: 100 })
    .withMessage('Title must be less than 100 characters')
    .trim(),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters')
    .trim(),
  
  body('category')
    .optional()
    .isIn(['2D', '3D', 'Mixed'])
    .withMessage('Category must be one of: 2D, 3D, Mixed'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  
  body('tags.*')
    .optional()
    .isLength({ max: 30 })
    .withMessage('Each tag must be less than 30 characters')
    .trim(),
  
  body('attributes')
    .optional()
    .isObject()
    .withMessage('Attributes must be an object')
]

/**
 * Validation rules for NFT minting
 */
const validateNFTMint = [
  body('to')
    .notEmpty()
    .withMessage('Recipient address is required')
    .matches(/^0x[a-fA-F0-9]{40}$/)
    .withMessage('Invalid Ethereum address format'),
  
  body('artworkId')
    .notEmpty()
    .withMessage('Artwork ID is required')
    .isMongoId()
    .withMessage('Invalid artwork ID format'),
  
  body('quantity')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Quantity must be between 1 and 100')
]

/**
 * Validation rules for batch NFT minting
 */
const validateBatchNFTMint = [
  body('to')
    .notEmpty()
    .withMessage('Recipient address is required')
    .matches(/^0x[a-fA-F0-9]{40}$/)
    .withMessage('Invalid Ethereum address format'),
  
  body('artworkIds')
    .isArray({ min: 1, max: 10 })
    .withMessage('Artwork IDs must be an array with 1-10 items'),
  
  body('artworkIds.*')
    .isMongoId()
    .withMessage('Invalid artwork ID format')
]

/**
 * Validation rules for transaction status query
 */
const validateTransactionStatus = [
  param('transactionHash')
    .matches(/^0x[a-fA-F0-9]{64}$/)
    .withMessage('Invalid transaction hash format')
]

/**
 * Validation rules for pagination
 */
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('sort')
    .optional()
    .isIn(['createdAt', '-createdAt', 'updatedAt', '-updatedAt', 'title', '-title'])
    .withMessage('Invalid sort field')
]

/**
 * Validation rules for search
 */
const validateSearch = [
  query('q')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters')
    .trim(),
  
  query('category')
    .optional()
    .isIn(['2D', '3D', 'Mixed'])
    .withMessage('Category must be one of: 2D, 3D, Mixed'),
  
  query('creator')
    .optional()
    .isMongoId()
    .withMessage('Invalid creator ID format')
]

/**
 * Validation rules for wallet operations
 */
const validateWalletOperation = [
  body('amount')
    .optional()
    .matches(/^\d+(\.\d+)?$/)
    .withMessage('Amount must be a valid number'),
  
  body('gasPrice')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Gas price must be a positive integer'),
  
  body('gasLimit')
    .optional()
    .isInt({ min: 21000 })
    .withMessage('Gas limit must be at least 21000')
]

/**
 * Validation rules for file upload
 */
const validateFileUpload = [
  body('fileType')
    .optional()
    .isIn(['image/jpeg', 'image/png', 'model/gltf-binary', 'application/octet-stream'])
    .withMessage('Invalid file type'),
  
  body('fileName')
    .optional()
    .isLength({ max: 255 })
    .withMessage('File name must be less than 255 characters')
    .matches(/^[a-zA-Z0-9._-]+$/)
    .withMessage('File name contains invalid characters')
]

module.exports = {
  validateRegister,
  validateLogin,
  validateProfileUpdate,
  validatePasswordChange,
  validateAccountDeactivation,
  validateArtworkUpload,
  validateNFTMint,
  validateBatchNFTMint,
  validateTransactionStatus,
  validatePagination,
  validateSearch,
  validateWalletOperation,
  validateFileUpload
}
