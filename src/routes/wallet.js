const express = require('express')
const router = express.Router()

// Import controllers
const walletController = require('../controllers/walletController')

// Import middleware
const { auth } = require('../middleware/auth')
const { 
  validateWalletOperation,
  validateTransactionStatus,
  validatePagination 
} = require('../middleware/validation')

/**
 * @route   GET /api/wallet/info
 * @desc    Get wallet information (balance, address, etc.)
 * @access  Private
 */
router.get('/info',
  auth,
  walletController.getWalletInfo
)

/**
 * @route   GET /api/wallet/transactions
 * @desc    Get transaction history
 * @access  Private
 */
router.get('/transactions',
  auth,
  validatePagination,
  walletController.getTransactionHistory
)

/**
 * @route   POST /api/wallet/estimate-gas
 * @desc    Estimate gas for a transaction
 * @access  Private
 */
router.post('/estimate-gas',
  auth,
  validateWalletOperation,
  walletController.estimateGas
)

/**
 * @route   POST /api/wallet/send-transaction
 * @desc    Send a transaction (use with caution)
 * @access  Private
 */
router.post('/send-transaction',
  auth,
  validateWalletOperation,
  walletController.sendTransaction
)

/**
 * @route   GET /api/wallet/transaction/:transactionHash
 * @desc    Get transaction receipt
 * @access  Private
 */
router.get('/transaction/:transactionHash',
  auth,
  validateTransactionStatus,
  walletController.getTransactionReceipt
)

/**
 * @route   GET /api/wallet/network-status
 * @desc    Get network status and gas prices
 * @access  Private
 */
router.get('/network-status',
  auth,
  walletController.getNetworkStatus
)

module.exports = router
