const express = require('express')
const router = express.Router()

// Import controllers
const nftController = require('../controllers/nftController')

// Import middleware
const { auth } = require('../middleware/auth')
const {
  validateNFTMint,
  validateBatchNFTMint,
  validateTransactionStatus
} = require('../middleware/validation')

/**
 * @route   POST /api/nft/mint
 * @desc    Mint NFT from artwork
 * @access  Private
 */
router.post('/mint',
  auth,
  validateNFTMint,
  nftController.mintNFT
)

/**
 * @route   POST /api/nft/batch-mint
 * @desc    Batch mint multiple NFTs
 * @access  Private
 */
router.post('/batch-mint',
  auth,
  validateBatchNFTMint,
  nftController.batchMintNFT
)

/**
 * @route   POST /api/nft/estimate-gas
 * @desc    Estimate gas for NFT minting
 * @access  Private
 */
router.post('/estimate-gas',
  auth,
  nftController.estimateMintingGas
)

/**
 * @route   GET /api/nft/transaction-status/:transactionHash
 * @desc    Get NFT transaction status
 * @access  Private
 */
router.get('/transaction-status/:transactionHash',
  auth,
  validateTransactionStatus,
  nftController.getTransactionStatus
)

/**
 * @route   GET /api/nft/user-nfts
 * @desc    Get user's minted NFTs
 * @access  Private
 */
router.get('/user-nfts',
  auth,
  nftController.getUserNFTs
)

/**
 * @route   GET /api/nft/contract-info
 * @desc    Get smart contract information
 * @access  Private
 */
router.get('/contract-info',
  auth,
  nftController.getContractInfo
)

module.exports = router
