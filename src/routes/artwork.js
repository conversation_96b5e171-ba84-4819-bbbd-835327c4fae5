const express = require('express')
const router = express.Router()

// Import controllers
const artworkController = require('../controllers/artworkController')

// Import middleware
const { auth, optionalAuth } = require('../middleware/auth')
const { uploadFields, cleanupOnError } = require('../middleware/upload')
const {
  validateArtworkUpload,
  validatePagination,
  validateSearch
} = require('../middleware/validation')

/**
 * @route   POST /api/artwork/upload
 * @desc    Upload artwork files and metadata to IPFS
 * @access  Private
 */
router.post('/upload',
  auth,
  cleanupOnError,
  uploadFields([
    { name: 'mainFile', maxCount: 1 },
    { name: 'thumbnail', maxCount: 1 },
    { name: 'additional', maxCount: 5 }
  ]),
  validateArtworkUpload,
  artworkController.uploadArtwork
)

/**
 * @route   GET /api/artwork
 * @desc    Get user's artworks
 * @access  Private
 */
router.get('/',
  auth,
  validatePagination,
  artworkController.getUserArtworks
)

/**
 * @route   GET /api/artwork/public
 * @desc    Get public artworks (gallery)
 * @access  Public
 */
router.get('/public',
  optionalAuth,
  validatePagination,
  validateSearch,
  artworkController.getPublicArtworks
)

/**
 * @route   GET /api/artwork/stats
 * @desc    Get artwork statistics for user
 * @access  Private
 */
router.get('/stats',
  auth,
  artworkController.getArtworkStats
)

/**
 * @route   GET /api/artwork/ipfs/:hash
 * @desc    Get IPFS content by hash
 * @access  Private
 */
router.get('/ipfs/:hash',
  auth,
  artworkController.getIPFSContent
)

/**
 * @route   GET /api/artwork/test-ipfs
 * @desc    Test IPFS connection
 * @access  Private
 */
router.get('/test-ipfs',
  auth,
  artworkController.testIPFS
)

/**
 * @route   GET /api/artwork/:id
 * @desc    Get specific artwork by ID
 * @access  Private
 */
router.get('/:id',
  auth,
  artworkController.getArtworkById
)

/**
 * @route   PUT /api/artwork/:id
 * @desc    Update artwork metadata
 * @access  Private
 */
router.put('/:id',
  auth,
  artworkController.updateArtwork
)

/**
 * @route   DELETE /api/artwork/:id
 * @desc    Delete artwork
 * @access  Private
 */
router.delete('/:id',
  auth,
  artworkController.deleteArtwork
)

module.exports = router
