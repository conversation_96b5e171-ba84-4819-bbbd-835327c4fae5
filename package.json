{"name": "spray-nft-backend", "version": "1.0.0", "description": "Backend system for Unity AR NFT minting on Polygon blockchain", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "deploy:testnet": "hardhat run scripts/deploy.js --network mumbai", "deploy:mainnet": "hardhat run scripts/deploy.js --network polygon", "compile": "hardhat compile", "verify": "hardhat verify"}, "keywords": ["nft", "polygon", "unity", "ar", "blockchain", "ipfs", "web3"], "author": "SprayNFT Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "ethers": "^6.8.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "winston": "^3.11.0"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.9", "@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-network-helpers": "^1.0.13", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomicfoundation/hardhat-verify": "^2.0.1", "@openzeppelin/contracts": "^5.0.0", "@openzeppelin/hardhat-upgrades": "^3.0.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.20", "@types/mocha": "^10.0.10", "chai": "^4.5.0", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "hardhat": "^2.19.2", "hardhat-gas-reporter": "^1.0.10", "jest": "^29.7.0", "nodemon": "^3.0.2", "solidity-coverage": "^0.8.16", "supertest": "^6.3.3", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}