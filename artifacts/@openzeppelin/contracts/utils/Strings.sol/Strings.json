{"_format": "hh-sol-artifact-1", "contractName": "Strings", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "StringsInsufficientHexLength", "type": "error"}, {"inputs": [], "name": "StringsInvalidAddressFormat", "type": "error"}, {"inputs": [], "name": "StringsInvalidChar", "type": "error"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212205cb8026103b37e335d367be611893cc85b642daf48902948c5c235464991ce0064736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212205cb8026103b37e335d367be611893cc85b642daf48902948c5c235464991ce0064736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}