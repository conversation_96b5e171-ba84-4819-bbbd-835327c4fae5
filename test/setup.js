const { MongoMemoryServer } = require('mongodb-memory-server')
const mongoose = require('mongoose')
const path = require('path')
const fs = require('fs')

// Test environment setup
process.env.NODE_ENV = 'test'
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-for-testing-only'
process.env.ENCRYPTION_KEY = '12345678901234567890123456789012'
process.env.PINATA_API_KEY = 'test-pinata-key'
process.env.PINATA_SECRET_KEY = 'test-pinata-secret'
process.env.POLYGON_RPC_URL = 'https://polygon-rpc.com'
process.env.MUMBAI_RPC_URL = 'https://rpc.ankr.com/polygon_amoy'
process.env.PRIVATE_KEY = '0xd6f513a45904a270f79a8dfd619acd93ab65da8a44a866d6d263f883868ac459'

let mongod

// Global test setup
before(async function() {
  this.timeout(60000) // Increase timeout for MongoDB setup
  
  console.log('Setting up test environment...')
  
  // Start in-memory MongoDB
  mongod = await MongoMemoryServer.create({
    binary: {
      version: '6.0.0'
    }
  })
  
  const mongoUri = mongod.getUri()
  process.env.MONGODB_URI = mongoUri
  
  console.log(`Test MongoDB started at: ${mongoUri}`)
  
  // Create test fixtures directory
  const fixturesDir = path.join(__dirname, 'fixtures')
  if (!fs.existsSync(fixturesDir)) {
    fs.mkdirSync(fixturesDir, { recursive: true })
  }
  
  // Create test upload directory
  const uploadsDir = path.join(__dirname, '../uploads/test')
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true })
  }
  
  console.log('Test environment setup complete')
})

// Global test cleanup
after(async function() {
  this.timeout(30000)
  
  console.log('Cleaning up test environment...')
  
  // Close MongoDB connection
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close()
  }
  
  // Stop in-memory MongoDB
  if (mongod) {
    await mongod.stop()
  }
  
  // Clean up test files
  const testUploadsDir = path.join(__dirname, '../uploads/test')
  if (fs.existsSync(testUploadsDir)) {
    fs.rmSync(testUploadsDir, { recursive: true, force: true })
  }
  
  const fixturesDir = path.join(__dirname, 'fixtures')
  if (fs.existsSync(fixturesDir)) {
    fs.rmSync(fixturesDir, { recursive: true, force: true })
  }
  
  console.log('Test environment cleanup complete')
})

// Test utilities
const testUtils = {
  // Create test user data
  createTestUser: (overrides = {}) => ({
    email: '<EMAIL>',
    username: 'testuser',
    password: 'TestPassword123',
    firstName: 'Test',
    lastName: 'User',
    ...overrides
  }),
  
  // Create test artwork data
  createTestArtwork: (overrides = {}) => ({
    title: 'Test Artwork',
    description: 'A test artwork for testing purposes',
    category: '2D',
    tags: ['test', 'artwork'],
    attributes: {
      dimensions: {
        width: 1920,
        height: 1080,
        depth: 0
      },
      colors: ['#FF0000', '#00FF00', '#0000FF'],
      style: 'digital',
      medium: 'digital',
      complexity: 'medium'
    },
    ...overrides
  }),
  
  // Create test image buffer
  createTestImage: () => {
    // Simple 1x1 PNG image
    return Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ])
  },
  
  // Create test file
  createTestFile: (filename = 'test.png', content = null) => {
    const fixturesDir = path.join(__dirname, 'fixtures')
    const filePath = path.join(fixturesDir, filename)
    
    if (!content) {
      content = testUtils.createTestImage()
    }
    
    fs.writeFileSync(filePath, content)
    return filePath
  },
  
  // Clean up test files
  cleanupTestFiles: () => {
    const fixturesDir = path.join(__dirname, 'fixtures')
    if (fs.existsSync(fixturesDir)) {
      const files = fs.readdirSync(fixturesDir)
      files.forEach(file => {
        fs.unlinkSync(path.join(fixturesDir, file))
      })
    }
  },
  
  // Wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate random string
  randomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },
  
  // Generate random email
  randomEmail: () => `test${testUtils.randomString(8)}@example.com`,
  
  // Generate random username
  randomUsername: () => `user${testUtils.randomString(6)}`,
  
  // Mock IPFS responses
  mockIPFSResponse: {
    success: true,
    data: {
      IpfsHash: 'QmTestHash123456789',
      PinSize: 1024,
      Timestamp: new Date().toISOString()
    }
  },
  
  // Mock blockchain responses
  mockBlockchainResponse: {
    transactionHash: '0x1234567890abcdef1234567890abcdef12345678',
    blockNumber: 12345678,
    gasUsed: '21000',
    status: 'success'
  },
  
  // Validate response structure
  validateApiResponse: (response, expectSuccess = true) => {
    const { expect } = require('chai')
    
    expect(response.body).to.have.property('success')
    expect(response.body.success).to.equal(expectSuccess)
    
    if (expectSuccess) {
      expect(response.body).to.have.property('data')
    } else {
      expect(response.body).to.have.property('message')
    }
  },
  
  // Database helpers
  db: {
    // Clear all collections
    clearAll: async () => {
      const collections = await mongoose.connection.db.collections()
      await Promise.all(collections.map(collection => collection.deleteMany({})))
    },
    
    // Clear specific collection
    clear: async (collectionName) => {
      await mongoose.connection.db.collection(collectionName).deleteMany({})
    },
    
    // Count documents in collection
    count: async (collectionName) => {
      return await mongoose.connection.db.collection(collectionName).countDocuments()
    }
  }
}

// Make test utilities globally available
global.testUtils = testUtils

// Chai configuration
const chai = require('chai')
chai.config.includeStack = true
chai.config.showDiff = true

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

// Suppress console.log during tests unless DEBUG is set
if (!process.env.DEBUG) {
  const originalLog = console.log
  const originalWarn = console.warn
  const originalError = console.error
  
  console.log = () => {}
  console.warn = () => {}
  console.error = (...args) => {
    // Still show errors in tests
    if (args[0] && args[0].includes && args[0].includes('Test')) {
      originalError(...args)
    }
  }
  
  // Restore console methods after tests
  after(() => {
    console.log = originalLog
    console.warn = originalWarn
    console.error = originalError
  })
}

module.exports = testUtils
