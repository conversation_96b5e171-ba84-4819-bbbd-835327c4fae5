const { expect } = require('chai')
const { ethers } = require('hardhat')

describe('SprayNFT', function () {
  let SprayNFT, sprayNFT, owner, addr1, addr2, creator

  const CONTRACT_NAME = 'SprayNFT'
  const CONTRACT_SYMBOL = 'SPRAY'
  const MAX_SUPPLY = 1000
  const MINTING_FEE = ethers.parseEther('0.001')
  const ROYALTY_FRACTION = 250 // 2.5%
  const SAMPLE_TOKEN_URI = 'ipfs://QmSampleHash123/metadata.json'

  beforeEach(async function () {
    // Get signers
    ;[owner, addr1, addr2, creator] = await ethers.getSigners()

    // Deploy contract
    SprayNFT = await ethers.getContractFactory('SprayNFT')
    sprayNFT = await SprayNFT.deploy(
      CONTRACT_NAME,
      CONTRACT_SYMBOL,
      MAX_SUPPLY,
      MINTING_FEE,
      owner.address,
      ROYALTY_FRACTION
    )
    await sprayNFT.waitForDeployment()
  })

  describe('Deployment', function () {
    it('Should set the correct name and symbol', async function () {
      expect(await sprayNFT.name()).to.equal(CONTRACT_NAME)
      expect(await sprayNFT.symbol()).to.equal(CONTRACT_SYMBOL)
    })

    it('Should set the correct owner', async function () {
      expect(await sprayNFT.owner()).to.equal(owner.address)
    })

    it('Should set the correct max supply', async function () {
      expect(await sprayNFT.maxSupply()).to.equal(MAX_SUPPLY)
    })

    it('Should set the correct minting fee', async function () {
      expect(await sprayNFT.mintingFee()).to.equal(MINTING_FEE)
    })

    it('Should start with zero total supply', async function () {
      expect(await sprayNFT.totalSupply()).to.equal(0)
    })
  })

  describe('Minting', function () {
    it('Should mint a token successfully', async function () {
      const tx = await sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, creator.address, {
        value: MINTING_FEE
      })

      await expect(tx)
        .to.emit(sprayNFT, 'TokenMinted')
        .withArgs(0, creator.address, addr1.address, SAMPLE_TOKEN_URI)

      expect(await sprayNFT.ownerOf(0)).to.equal(addr1.address)
      expect(await sprayNFT.tokenURI(0)).to.equal(SAMPLE_TOKEN_URI)
      expect(await sprayNFT.tokenCreators(0)).to.equal(creator.address)
      expect(await sprayNFT.totalSupply()).to.equal(1)
      expect(await sprayNFT.creatorTokenCount(creator.address)).to.equal(1)
    })

    it('Should fail to mint without sufficient fee', async function () {
      await expect(
        sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, creator.address, {
          value: MINTING_FEE - BigInt(1)
        })
      ).to.be.revertedWith('SprayNFT: insufficient minting fee')
    })

    it('Should fail to mint with empty token URI', async function () {
      await expect(
        sprayNFT.mint(addr1.address, '', creator.address, {
          value: MINTING_FEE
        })
      ).to.be.revertedWith('SprayNFT: empty token URI')
    })

    it('Should fail to mint to zero address', async function () {
      await expect(
        sprayNFT.mint(ethers.ZeroAddress, SAMPLE_TOKEN_URI, creator.address, {
          value: MINTING_FEE
        })
      ).to.be.revertedWith('SprayNFT: mint to zero address')
    })

    it('Should fail to mint with zero creator address', async function () {
      await expect(
        sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, ethers.ZeroAddress, {
          value: MINTING_FEE
        })
      ).to.be.revertedWith('SprayNFT: creator is zero address')
    })
  })

  describe('Batch Minting', function () {
    it('Should batch mint tokens successfully', async function () {
      const tokenURIs = [
        'ipfs://QmSampleHash1/metadata.json',
        'ipfs://QmSampleHash2/metadata.json',
        'ipfs://QmSampleHash3/metadata.json'
      ]

      const tx = await sprayNFT.batchMint(addr1.address, tokenURIs, creator.address, {
        value: MINTING_FEE * BigInt(tokenURIs.length)
      })

      // Check events
      for (let i = 0; i < tokenURIs.length; i++) {
        await expect(tx)
          .to.emit(sprayNFT, 'TokenMinted')
          .withArgs(i, creator.address, addr1.address, tokenURIs[i])
      }

      // Check token ownership and metadata
      for (let i = 0; i < tokenURIs.length; i++) {
        expect(await sprayNFT.ownerOf(i)).to.equal(addr1.address)
        expect(await sprayNFT.tokenURI(i)).to.equal(tokenURIs[i])
        expect(await sprayNFT.tokenCreators(i)).to.equal(creator.address)
      }

      expect(await sprayNFT.totalSupply()).to.equal(tokenURIs.length)
      expect(await sprayNFT.creatorTokenCount(creator.address)).to.equal(tokenURIs.length)
    })

    it('Should fail batch mint without sufficient fee', async function () {
      const tokenURIs = ['ipfs://QmSampleHash1/metadata.json', 'ipfs://QmSampleHash2/metadata.json']

      await expect(
        sprayNFT.batchMint(addr1.address, tokenURIs, creator.address, {
          value: MINTING_FEE
        })
      ).to.be.revertedWith('SprayNFT: insufficient minting fee')
    })
  })

  describe('Royalties', function () {
    beforeEach(async function () {
      // Mint a token for testing
      await sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, creator.address, {
        value: MINTING_FEE
      })
    })

    it('Should return default royalty info', async function () {
      const salePrice = ethers.parseEther('1')
      const [recipient, royaltyAmount] = await sprayNFT.royaltyInfo(0, salePrice)

      expect(recipient).to.equal(owner.address)
      expect(royaltyAmount).to.equal(salePrice * BigInt(ROYALTY_FRACTION) / BigInt(10000))
    })

    it('Should set token-specific royalty', async function () {
      const newRoyaltyFraction = 500 // 5%
      
      await sprayNFT.connect(creator).setTokenRoyalty(0, creator.address, newRoyaltyFraction)

      const salePrice = ethers.parseEther('1')
      const [recipient, royaltyAmount] = await sprayNFT.royaltyInfo(0, salePrice)

      expect(recipient).to.equal(creator.address)
      expect(royaltyAmount).to.equal(salePrice * BigInt(newRoyaltyFraction) / BigInt(10000))
    })

    it('Should fail to set royalty by unauthorized user', async function () {
      await expect(
        sprayNFT.connect(addr2).setTokenRoyalty(0, addr2.address, 500)
      ).to.be.revertedWith('SprayNFT: not authorized to set royalty')
    })
  })

  describe('Access Control', function () {
    it('Should allow owner to pause and unpause', async function () {
      await sprayNFT.pause()
      expect(await sprayNFT.paused()).to.be.true

      await expect(
        sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, creator.address, {
          value: MINTING_FEE
        })
      ).to.be.revertedWithCustomError(sprayNFT, 'EnforcedPause')

      await sprayNFT.unpause()
      expect(await sprayNFT.paused()).to.be.false

      await expect(
        sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, creator.address, {
          value: MINTING_FEE
        })
      ).to.not.be.reverted
    })

    it('Should allow owner to update minting fee', async function () {
      const newFee = ethers.parseEther('0.002')
      
      await expect(sprayNFT.setMintingFee(newFee))
        .to.emit(sprayNFT, 'MintingFeeUpdated')
        .withArgs(newFee)

      expect(await sprayNFT.mintingFee()).to.equal(newFee)
    })

    it('Should allow owner to update max supply', async function () {
      const newMaxSupply = 2000
      
      await expect(sprayNFT.setMaxSupply(newMaxSupply))
        .to.emit(sprayNFT, 'MaxSupplyUpdated')
        .withArgs(newMaxSupply)

      expect(await sprayNFT.maxSupply()).to.equal(newMaxSupply)
    })

    it('Should fail to update max supply below current supply', async function () {
      // Mint two tokens first
      await sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, creator.address, {
        value: MINTING_FEE
      })
      await sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI + '2', creator.address, {
        value: MINTING_FEE
      })

      // Current supply is 2, so setting max supply to 1 should fail
      await expect(
        sprayNFT.setMaxSupply(1)
      ).to.be.revertedWith('SprayNFT: max supply too low')
    })
  })

  describe('Withdrawal', function () {
    it('Should allow owner to withdraw funds', async function () {
      // Mint some tokens to generate fees
      await sprayNFT.mint(addr1.address, SAMPLE_TOKEN_URI, creator.address, {
        value: MINTING_FEE
      })

      const initialBalance = await ethers.provider.getBalance(owner.address)
      const contractBalance = await ethers.provider.getBalance(await sprayNFT.getAddress())

      const tx = await sprayNFT.withdraw()
      const receipt = await tx.wait()
      const gasUsed = receipt.gasUsed * receipt.gasPrice

      const finalBalance = await ethers.provider.getBalance(owner.address)
      expect(finalBalance).to.equal(initialBalance + contractBalance - gasUsed)
    })

    it('Should fail to withdraw with no funds', async function () {
      await expect(sprayNFT.withdraw()).to.be.revertedWith('SprayNFT: no funds to withdraw')
    })
  })

  describe('Token Queries', function () {
    beforeEach(async function () {
      // Mint some tokens
      await sprayNFT.mint(addr1.address, 'ipfs://token1', creator.address, { value: MINTING_FEE })
      await sprayNFT.mint(addr1.address, 'ipfs://token2', creator.address, { value: MINTING_FEE })
      await sprayNFT.mint(addr2.address, 'ipfs://token3', creator.address, { value: MINTING_FEE })
    })

    it('Should return tokens owned by an address', async function () {
      const addr1Tokens = await sprayNFT.tokensOfOwner(addr1.address)
      const addr2Tokens = await sprayNFT.tokensOfOwner(addr2.address)

      expect(addr1Tokens.length).to.equal(2)
      expect(addr1Tokens[0]).to.equal(0)
      expect(addr1Tokens[1]).to.equal(1)

      expect(addr2Tokens.length).to.equal(1)
      expect(addr2Tokens[0]).to.equal(2)
    })
  })
})
