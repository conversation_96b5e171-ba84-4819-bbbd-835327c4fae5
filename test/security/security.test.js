const request = require('supertest')
const { expect } = require('chai')
const app = require('../../src/server')
const User = require('../../src/models/User')
const { connectDB, disconnectDB } = require('../../src/config/database')

describe('Security Tests', () => {
  let server
  let testUser
  let authToken

  before(async () => {
    await connectDB()
    server = app.listen(0)
  })

  after(async () => {
    if (server) {
      server.close()
    }
    await disconnectDB()
  })

  beforeEach(async () => {
    await User.deleteMany({})
    
    // Create test user
    const userData = {
      email: '<EMAIL>',
      username: 'testuser',
      password: 'TestPassword123'
    }

    await request(app)
      .post('/api/auth/register')
      .send(userData)

    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ email: userData.email, password: userData.password })

    testUser = loginResponse.body.data.user
    authToken = loginResponse.body.data.token
  })

  describe('Rate Limiting', () => {
    it('should enforce rate limits on authentication endpoints', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        const response = await request(app)
          .post('/api/auth/login')
          .send(loginData)

        if (i < 5) {
          expect(response.status).to.equal(401)
        } else {
          expect(response.status).to.equal(429)
          expect(response.body.message).to.include('Too many authentication attempts')
        }
      }
    })

    it('should enforce general rate limits', async () => {
      // This test would need to be adjusted based on actual rate limits
      // For demonstration, we'll test that rate limiting middleware is present
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.headers).to.have.property('x-ratelimit-limit')
      expect(response.headers).to.have.property('x-ratelimit-remaining')
    })
  })

  describe('Input Sanitization', () => {
    it('should sanitize XSS attempts in registration', async () => {
      const maliciousData = {
        email: '<EMAIL>',
        username: '<script>alert("xss")</script>',
        password: 'TestPassword123',
        firstName: '<img src=x onerror=alert("xss")>',
        lastName: 'User'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(maliciousData)

      if (response.status === 201) {
        expect(response.body.data.user.username).to.not.include('<script>')
        expect(response.body.data.user.profile.firstName).to.not.include('onerror')
      }
    })

    it('should prevent NoSQL injection attempts', async () => {
      const injectionData = {
        email: { $ne: null },
        password: { $ne: null }
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(injectionData)
        .expect(401)

      expect(response.body.success).to.be.false
    })

    it('should sanitize dangerous object keys', async () => {
      const maliciousData = {
        title: 'Test Artwork',
        description: 'Test description',
        '$where': 'function() { return true; }',
        'constructor.prototype.isAdmin': true
      }

      const response = await request(app)
        .post('/api/artwork/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('title', maliciousData.title)
        .field('description', maliciousData.description)
        .field('$where', maliciousData.$where)
        .field('constructor.prototype.isAdmin', maliciousData['constructor.prototype.isAdmin'])

      // Should not include dangerous keys in processed data
      if (response.status === 201) {
        expect(response.body.data.artwork).to.not.have.property('$where')
        expect(response.body.data.artwork).to.not.have.property('constructor.prototype.isAdmin')
      }
    })
  })

  describe('Authentication Security', () => {
    it('should require valid JWT tokens', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body.success).to.be.false
    })

    it('should reject expired tokens', async () => {
      // This would require creating an expired token for testing
      // For now, we'll test with a malformed token
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer expired.token.here')
        .expect(401)

      expect(response.body.success).to.be.false
    })

    it('should require authorization header format', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', authToken) // Missing 'Bearer ' prefix
        .expect(401)

      expect(response.body.success).to.be.false
    })
  })

  describe('File Upload Security', () => {
    it('should reject files that are too large', async () => {
      // Create a large buffer (simulating large file)
      const largeBuffer = Buffer.alloc(101 * 1024 * 1024) // 101MB

      const response = await request(app)
        .post('/api/artwork/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('title', 'Test Artwork')
        .attach('mainFile', largeBuffer, 'large-file.png')
        .expect(400)

      expect(response.body.success).to.be.false
      expect(response.body.message).to.include('File too large')
    })

    it('should reject unauthorized file types', async () => {
      const maliciousBuffer = Buffer.from('#!/bin/bash\necho "malicious script"')

      const response = await request(app)
        .post('/api/artwork/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('title', 'Test Artwork')
        .attach('mainFile', maliciousBuffer, 'malicious.sh')
        .expect(400)

      expect(response.body.success).to.be.false
      expect(response.body.message).to.include('File type')
    })

    it('should sanitize filenames', async () => {
      const testBuffer = Buffer.from('test image data')
      const maliciousFilename = '../../../etc/passwd'

      const response = await request(app)
        .post('/api/artwork/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('title', 'Test Artwork')
        .attach('mainFile', testBuffer, maliciousFilename)

      // Should either reject or sanitize the filename
      if (response.status === 201) {
        expect(response.body.data.artwork.files.main.originalName).to.not.include('../')
      } else {
        expect(response.status).to.equal(400)
      }
    })
  })

  describe('Authorization', () => {
    it('should prevent users from accessing other users\' data', async () => {
      // Create another user
      const otherUserData = {
        email: '<EMAIL>',
        username: 'otheruser',
        password: 'TestPassword123'
      }

      await request(app)
        .post('/api/auth/register')
        .send(otherUserData)

      const otherLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({ email: otherUserData.email, password: otherUserData.password })

      const otherToken = otherLoginResponse.body.data.token

      // Try to access first user's profile with second user's token
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${otherToken}`)
        .expect(200)

      // Should return the second user's profile, not the first user's
      expect(response.body.data.user.email).to.equal(otherUserData.email)
      expect(response.body.data.user.email).to.not.equal(testUser.email)
    })

    it('should prevent privilege escalation', async () => {
      // Try to update user role (if such endpoint existed)
      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          role: 'admin',
          isAdmin: true,
          permissions: ['all']
        })

      // Should either ignore these fields or reject the request
      if (response.status === 200) {
        expect(response.body.data.user).to.not.have.property('role')
        expect(response.body.data.user).to.not.have.property('isAdmin')
      }
    })
  })

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)

      expect(response.headers).to.have.property('x-content-type-options', 'nosniff')
      expect(response.headers).to.have.property('x-frame-options', 'DENY')
      expect(response.headers).to.have.property('x-xss-protection', '1; mode=block')
      expect(response.headers).to.not.have.property('x-powered-by')
    })

    it('should set CORS headers appropriately', async () => {
      const response = await request(app)
        .options('/api/auth/profile')
        .set('Origin', 'http://localhost:3000')

      expect(response.headers).to.have.property('access-control-allow-origin')
      expect(response.headers).to.have.property('access-control-allow-methods')
    })
  })

  describe('Error Handling', () => {
    it('should not expose sensitive information in errors', async () => {
      const response = await request(app)
        .get('/api/nonexistent-endpoint')
        .expect(404)

      // Should not expose stack traces or internal paths
      expect(response.body).to.not.have.property('stack')
      expect(response.body).to.not.have.property('path')
    })

    it('should handle database errors gracefully', async () => {
      // Try to create user with invalid data that would cause DB error
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>', // Duplicate email
          username: 'testuser2',
          password: 'TestPassword123'
        })

      expect(response.status).to.be.oneOf([400, 409])
      expect(response.body.success).to.be.false
      expect(response.body).to.not.have.property('stack')
    })
  })

  describe('Session Security', () => {
    it('should invalidate tokens on logout', async () => {
      // Logout user
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ refreshToken: 'dummy-refresh-token' })
        .expect(200)

      // Try to use the same token
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(401)

      expect(response.body.success).to.be.false
    })

    it('should handle concurrent login attempts', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'TestPassword123'
      }

      // Make multiple concurrent login requests
      const promises = Array(5).fill().map(() =>
        request(app)
          .post('/api/auth/login')
          .send(loginData)
      )

      const responses = await Promise.all(promises)

      // All should succeed (or be rate limited)
      responses.forEach(response => {
        expect(response.status).to.be.oneOf([200, 429])
      })
    })
  })
})
