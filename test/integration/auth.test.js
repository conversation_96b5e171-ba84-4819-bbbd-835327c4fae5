const request = require('supertest')
const { expect } = require('chai')
const app = require('../../src/server')
const User = require('../../src/models/User')
const { connectDB, disconnectDB } = require('../../src/config/database')

describe('Authentication Integration Tests', () => {
  let server
  let testUser
  let authToken
  let refreshToken

  before(async () => {
    // Connect to test database
    await connectDB()
    
    // Start server
    server = app.listen(0) // Use random port
  })

  after(async () => {
    // Clean up
    if (server) {
      server.close()
    }
    await disconnectDB()
  })

  beforeEach(async () => {
    // Clean up test data
    await User.deleteMany({})
  })

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'TestPassword123',
        firstName: 'Test',
        lastName: 'User'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      expect(response.body.success).to.be.true
      expect(response.body.data.user.email).to.equal(userData.email)
      expect(response.body.data.user.username).to.equal(userData.username)
      expect(response.body.data.token).to.be.a('string')
      expect(response.body.data.refreshToken).to.be.a('string')
      expect(response.body.data.wallet.address).to.match(/^0x[a-fA-F0-9]{40}$/)

      // Verify user was created in database
      const user = await User.findOne({ email: userData.email })
      expect(user).to.exist
      expect(user.wallet.address).to.exist
    })

    it('should fail with invalid email', async () => {
      const userData = {
        email: 'invalid-email',
        username: 'testuser',
        password: 'TestPassword123'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400)

      expect(response.body.success).to.be.false
      expect(response.body.errors).to.be.an('array')
    })

    it('should fail with weak password', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'weak'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400)

      expect(response.body.success).to.be.false
    })

    it('should fail with duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'TestPassword123'
      }

      // Register first user
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      // Try to register with same email
      const duplicateData = {
        ...userData,
        username: 'differentuser'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(duplicateData)
        .expect(400)

      expect(response.body.success).to.be.false
      expect(response.body.message).to.include('email')
    })
  })

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create test user
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'TestPassword123'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)

      testUser = response.body.data.user
    })

    it('should login successfully with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'TestPassword123'
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.user.email).to.equal(loginData.email)
      expect(response.body.data.token).to.be.a('string')
      expect(response.body.data.refreshToken).to.be.a('string')

      authToken = response.body.data.token
      refreshToken = response.body.data.refreshToken
    })

    it('should fail with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'TestPassword123'
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401)

      expect(response.body.success).to.be.false
    })

    it('should fail with invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword'
      }

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401)

      expect(response.body.success).to.be.false
    })
  })

  describe('GET /api/auth/profile', () => {
    beforeEach(async () => {
      // Create and login test user
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'TestPassword123'
      }

      await request(app)
        .post('/api/auth/register')
        .send(userData)

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ email: userData.email, password: userData.password })

      authToken = loginResponse.body.data.token
    })

    it('should get user profile with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.user.email).to.equal('<EMAIL>')
      expect(response.body.data.wallet.address).to.exist
    })

    it('should fail without token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401)

      expect(response.body.success).to.be.false
    })

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body.success).to.be.false
    })
  })

  describe('POST /api/auth/refresh', () => {
    beforeEach(async () => {
      // Create and login test user
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'TestPassword123'
      }

      await request(app)
        .post('/api/auth/register')
        .send(userData)

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ email: userData.email, password: userData.password })

      refreshToken = loginResponse.body.data.refreshToken
    })

    it('should refresh token successfully', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.token).to.be.a('string')
      expect(response.body.data.refreshToken).to.be.a('string')
    })

    it('should fail with invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401)

      expect(response.body.success).to.be.false
    })
  })

  describe('POST /api/auth/logout', () => {
    beforeEach(async () => {
      // Create and login test user
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'TestPassword123'
      }

      await request(app)
        .post('/api/auth/register')
        .send(userData)

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ email: userData.email, password: userData.password })

      authToken = loginResponse.body.data.token
      refreshToken = loginResponse.body.data.refreshToken
    })

    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ refreshToken })
        .expect(200)

      expect(response.body.success).to.be.true
    })
  })
})
