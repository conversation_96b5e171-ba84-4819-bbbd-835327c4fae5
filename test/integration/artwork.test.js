const request = require('supertest')
const { expect } = require('chai')
const path = require('path')
const fs = require('fs')
const app = require('../../src/server')
const User = require('../../src/models/User')
const Artwork = require('../../src/models/Artwork')
const { connectDB, disconnectDB } = require('../../src/config/database')

describe('Artwork Integration Tests', () => {
  let server
  let testUser
  let authToken
  let testImagePath
  let testArtwork

  before(async () => {
    // Connect to test database
    await connectDB()
    
    // Start server
    server = app.listen(0)
    
    // Create test image file
    testImagePath = path.join(__dirname, '../fixtures/test-image.png')
    if (!fs.existsSync(path.dirname(testImagePath))) {
      fs.mkdirSync(path.dirname(testImagePath), { recursive: true })
    }
    
    // Create a simple test image (1x1 PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ])
    fs.writeFileSync(testImagePath, testImageBuffer)
  })

  after(async () => {
    // Clean up
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath)
    }
    if (server) {
      server.close()
    }
    await disconnectDB()
  })

  beforeEach(async () => {
    // Clean up test data
    await User.deleteMany({})
    await Artwork.deleteMany({})
    
    // Create and login test user
    const userData = {
      email: '<EMAIL>',
      username: 'testuser',
      password: 'TestPassword123'
    }

    await request(app)
      .post('/api/auth/register')
      .send(userData)

    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ email: userData.email, password: userData.password })

    testUser = loginResponse.body.data.user
    authToken = loginResponse.body.data.token
  })

  describe('POST /api/artwork/upload', () => {
    it('should upload artwork successfully', async () => {
      const response = await request(app)
        .post('/api/artwork/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('title', 'Test Artwork')
        .field('description', 'A test artwork')
        .field('category', '2D')
        .field('tags', JSON.stringify(['test', 'artwork']))
        .attach('mainFile', testImagePath)
        .expect(201)

      expect(response.body.success).to.be.true
      expect(response.body.data.artwork.title).to.equal('Test Artwork')
      expect(response.body.data.artwork.creator).to.equal(testUser._id)
      expect(response.body.data.artwork.status).to.equal('ready')
      
      testArtwork = response.body.data.artwork
    })

    it('should fail without authentication', async () => {
      const response = await request(app)
        .post('/api/artwork/upload')
        .field('title', 'Test Artwork')
        .attach('mainFile', testImagePath)
        .expect(401)

      expect(response.body.success).to.be.false
    })

    it('should fail without required fields', async () => {
      const response = await request(app)
        .post('/api/artwork/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('mainFile', testImagePath)
        .expect(400)

      expect(response.body.success).to.be.false
    })

    it('should fail without files', async () => {
      const response = await request(app)
        .post('/api/artwork/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('title', 'Test Artwork')
        .expect(400)

      expect(response.body.success).to.be.false
      expect(response.body.message).to.include('No files uploaded')
    })
  })

  describe('GET /api/artwork', () => {
    beforeEach(async () => {
      // Create test artwork
      const artwork = new Artwork({
        title: 'Test Artwork',
        description: 'A test artwork',
        creator: testUser._id,
        category: '2D',
        status: 'ready',
        files: {
          main: {
            originalName: 'test.png',
            mimeType: 'image/png',
            size: 1024,
            ipfsHash: 'QmTestHash',
            ipfsUrl: 'https://ipfs.io/ipfs/QmTestHash'
          }
        },
        metadata: {
          ipfsHash: 'QmMetadataHash',
          ipfsUrl: 'https://ipfs.io/ipfs/QmMetadataHash'
        }
      })
      
      testArtwork = await artwork.save()
    })

    it('should get user artworks successfully', async () => {
      const response = await request(app)
        .get('/api/artwork')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.artworks).to.be.an('array')
      expect(response.body.data.artworks).to.have.length(1)
      expect(response.body.data.artworks[0].title).to.equal('Test Artwork')
    })

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/artwork?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.pagination).to.exist
      expect(response.body.data.pagination.page).to.equal(1)
      expect(response.body.data.pagination.limit).to.equal(10)
    })

    it('should filter by category', async () => {
      const response = await request(app)
        .get('/api/artwork?category=2D')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.artworks).to.have.length(1)
    })

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/artwork')
        .expect(401)

      expect(response.body.success).to.be.false
    })
  })

  describe('GET /api/artwork/:id', () => {
    beforeEach(async () => {
      // Create test artwork
      const artwork = new Artwork({
        title: 'Test Artwork',
        description: 'A test artwork',
        creator: testUser._id,
        category: '2D',
        status: 'ready'
      })
      
      testArtwork = await artwork.save()
    })

    it('should get specific artwork successfully', async () => {
      const response = await request(app)
        .get(`/api/artwork/${testArtwork._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.artwork._id).to.equal(testArtwork._id.toString())
      expect(response.body.data.artwork.title).to.equal('Test Artwork')
    })

    it('should fail with invalid artwork ID', async () => {
      const response = await request(app)
        .get('/api/artwork/invalid-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(500)

      expect(response.body.success).to.be.false
    })

    it('should fail with non-existent artwork', async () => {
      const fakeId = '507f1f77bcf86cd799439011'
      const response = await request(app)
        .get(`/api/artwork/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404)

      expect(response.body.success).to.be.false
    })
  })

  describe('PUT /api/artwork/:id', () => {
    beforeEach(async () => {
      // Create test artwork
      const artwork = new Artwork({
        title: 'Test Artwork',
        description: 'A test artwork',
        creator: testUser._id,
        category: '2D',
        status: 'ready'
      })
      
      testArtwork = await artwork.save()
    })

    it('should update artwork successfully', async () => {
      const updateData = {
        title: 'Updated Artwork',
        description: 'Updated description',
        visibility: 'public'
      }

      const response = await request(app)
        .put(`/api/artwork/${testArtwork._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.artwork.title).to.equal('Updated Artwork')
      expect(response.body.data.artwork.description).to.equal('Updated description')
      expect(response.body.data.artwork.visibility).to.equal('public')
    })

    it('should fail to update other user\'s artwork', async () => {
      // Create another user
      const otherUserData = {
        email: '<EMAIL>',
        username: 'otheruser',
        password: 'TestPassword123'
      }

      await request(app)
        .post('/api/auth/register')
        .send(otherUserData)

      const otherLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({ email: otherUserData.email, password: otherUserData.password })

      const otherToken = otherLoginResponse.body.data.token

      const response = await request(app)
        .put(`/api/artwork/${testArtwork._id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .send({ title: 'Hacked Title' })
        .expect(403)

      expect(response.body.success).to.be.false
    })
  })

  describe('DELETE /api/artwork/:id', () => {
    beforeEach(async () => {
      // Create test artwork
      const artwork = new Artwork({
        title: 'Test Artwork',
        description: 'A test artwork',
        creator: testUser._id,
        category: '2D',
        status: 'ready'
      })
      
      testArtwork = await artwork.save()
    })

    it('should delete artwork successfully', async () => {
      const response = await request(app)
        .delete(`/api/artwork/${testArtwork._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).to.be.true

      // Verify artwork was deleted
      const deletedArtwork = await Artwork.findById(testArtwork._id)
      expect(deletedArtwork).to.be.null
    })

    it('should fail to delete minted artwork', async () => {
      // Mark artwork as minted
      testArtwork.nft.isMinted = true
      await testArtwork.save()

      const response = await request(app)
        .delete(`/api/artwork/${testArtwork._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400)

      expect(response.body.success).to.be.false
      expect(response.body.message).to.include('minted')
    })
  })

  describe('GET /api/artwork/public', () => {
    beforeEach(async () => {
      // Create public artwork
      const artwork = new Artwork({
        title: 'Public Artwork',
        description: 'A public artwork',
        creator: testUser._id,
        category: '2D',
        status: 'ready',
        visibility: 'public'
      })
      
      await artwork.save()
    })

    it('should get public artworks without authentication', async () => {
      const response = await request(app)
        .get('/api/artwork/public')
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.artworks).to.be.an('array')
      expect(response.body.data.artworks).to.have.length(1)
      expect(response.body.data.artworks[0].title).to.equal('Public Artwork')
    })

    it('should support search', async () => {
      const response = await request(app)
        .get('/api/artwork/public?search=Public')
        .expect(200)

      expect(response.body.success).to.be.true
      expect(response.body.data.artworks).to.have.length(1)
    })
  })
})
