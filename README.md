# SprayNFT Backend

> **Complete backend system for Unity AR NFT minting on Polygon blockchain**

[![Node.js](https://img.shields.io/badge/Node.js-18.x-green.svg)](https://nodejs.org/)
[![MongoDB](https://img.shields.io/badge/MongoDB-6.x-green.svg)](https://www.mongodb.com/)
[![Polygon](https://img.shields.io/badge/Polygon-Mainnet-purple.svg)](https://polygon.technology/)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)

A production-ready backend system that enables Unity AR applications to mint NFTs on the Polygon blockchain with IPFS storage, user authentication, and comprehensive security features.

## 🚀 Features

- **🎨 NFT Minting**: Complete ERC-721 NFT minting pipeline
- **🔐 User Authentication**: Secure JWT-based auth with backend-managed wallets
- **☁️ IPFS Storage**: Decentralized file storage via Pinata
- **⛓️ Blockchain Integration**: Polygon mainnet and testnet support
- **🎮 Unity Integration**: Complete C# SDK for Unity developers
- **🛡️ Enterprise Security**: Rate limiting, input validation, and comprehensive protection
- **📊 Monitoring**: Built-in health checks and metrics
- **🐳 Production Ready**: Docker containerization and deployment automation

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Unity Integration](#unity-integration)
- [Deployment](#deployment)
- [Testing](#testing)
- [Contributing](#contributing)
- [License](#license)

## ⚡ Quick Start

### Prerequisites

- **Node.js** 18.x or higher
- **MongoDB** 6.x or higher
- **npm** 9.x or higher

### 1. Clone and Install

```bash
git clone https://github.com/your-org/spray-nft-backend.git
cd spray-nft-backend
npm install
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit with your configuration
nano .env
```

### 3. Start Development Server

```bash
# Start MongoDB (if running locally)
mongod

# Start the application
npm run dev
```

The server will start at `http://localhost:3001`

## 🔧 Installation

### Method 1: Docker (Recommended)

```bash
# Clone repository
git clone https://github.com/your-org/spray-nft-backend.git
cd spray-nft-backend

# Copy and configure environment
cp .env.example .env
# Edit .env with your settings

# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### Method 2: Manual Installation

```bash
# Install dependencies
npm install

# Install MongoDB
# Ubuntu/Debian:
sudo apt update
sudo apt install -y mongodb

# macOS:
brew install mongodb-community

# Start MongoDB
sudo systemctl start mongodb  # Linux
brew services start mongodb-community  # macOS

# Compile smart contracts
npm run compile

# Start application
npm run dev
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following configuration:

```bash
# Application
NODE_ENV=development
PORT=3001

# Database
MONGODB_URI=mongodb://localhost:27017/spraynft

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here
JWT_EXPIRE=15m
JWT_REFRESH_EXPIRE=7d

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here

# IPFS/Pinata Configuration
PINATA_API_KEY=your-pinata-api-key
PINATA_SECRET_KEY=your-pinata-secret-key

# Blockchain Configuration
POLYGON_RPC_URL=https://polygon-rpc.com
MUMBAI_RPC_URL=https://rpc.ankr.com/polygon_amoy
PRIVATE_KEY=your-deployer-private-key-here

# Smart Contract Addresses (set after deployment)
NFT_CONTRACT_ADDRESS_POLYGON=
NFT_CONTRACT_ADDRESS_MUMBAI=

# Security (Optional)
ALLOWED_ORIGINS=http://localhost:3000
REQUIRE_API_KEY=false
IP_WHITELIST=
LOG_LEVEL=info
```

### Generate Secure Keys

```bash
# Generate JWT secrets (64 bytes)
node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(64).toString('hex'))"
node -e "console.log('JWT_REFRESH_SECRET=' + require('crypto').randomBytes(64).toString('hex'))"

# Generate encryption key (32 bytes)
node -e "console.log('ENCRYPTION_KEY=' + require('crypto').randomBytes(16).toString('hex'))"
```

### Smart Contract Deployment

```bash
# Deploy to testnet (Amoy)
npm run deploy:testnet

# Deploy to mainnet (after testing)
npm run deploy:mainnet

# Verify contracts
npm run verify
```

## 📚 API Documentation

### Authentication Endpoints

```bash
# Register user
POST /api/auth/register
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "SecurePassword123",
  "firstName": "John",
  "lastName": "Doe"
}

# Login user
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}

# Get user profile
GET /api/auth/profile
Authorization: Bearer <token>
```

### Artwork Endpoints

```bash
# Upload artwork
POST /api/artwork/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

# Get user artworks
GET /api/artwork
Authorization: Bearer <token>

# Get public artworks
GET /api/artwork/public
```

### NFT Endpoints

```bash
# Mint NFT
POST /api/nft/mint
Authorization: Bearer <token>
{
  "to": "0x...",
  "artworkId": "artwork-id"
}

# Get user NFTs
GET /api/nft/user-nfts
Authorization: Bearer <token>

# Get transaction status
GET /api/nft/transaction-status/:hash
Authorization: Bearer <token>
```

### Health & Monitoring

```bash
# Health check
GET /health

# System metrics
GET /metrics
```

## 🎮 Unity Integration

### Installation

1. Copy Unity scripts from `unity-integration/Scripts/` to your Unity project
2. Configure the API URL in `SprayNFTConfig.cs`
3. Add the `SprayNFTManager` to your scene

### Basic Usage

```csharp
// Initialize
SprayNFTManager.Instance.Initialize();

// Register user
var registerData = new RegisterRequest
{
    email = "<EMAIL>",
    username = "testuser",
    password = "SecurePassword123"
};

await SprayNFTManager.Instance.Auth.Register(registerData);

// Upload artwork
var artworkData = new ArtworkUploadRequest
{
    title = "My AR Creation",
    description = "Created in Unity AR",
    category = "3D"
};

var result = await SprayNFTManager.Instance.Artwork.UploadArtwork(
    artworkData, filePath, thumbnailPath
);

// Mint NFT
var mintRequest = new MintNFTRequest
{
    to = userWalletAddress,
    artworkId = artworkId
};

await SprayNFTManager.Instance.Minting.MintNFT(mintRequest);
```

See [Unity Integration Guide](unity-integration/README.md) for complete documentation.

## 🚀 Deployment

### Production Deployment

```bash
# Using deployment script
node scripts/deploy-production.js

# Or using Docker
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Setup

1. **Server Requirements**:
   - Ubuntu 20.04+ or CentOS 8+
   - 4GB+ RAM, 50GB+ SSD
   - Node.js 18.x, MongoDB 6.x

2. **SSL Certificate**:
   ```bash
   # Using Let's Encrypt
   sudo certbot --nginx -d yourdomain.com
   ```

3. **Firewall Configuration**:
   ```bash
   # Allow necessary ports
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw enable
   ```

See [Deployment Guide](DEPLOYMENT.md) for complete instructions.

## 🧪 Testing

### Run Tests

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:integration
npm run test:security
npm run test:unit

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

### Test Structure

```
test/
├── integration/     # API integration tests
├── security/        # Security and vulnerability tests
├── unit/           # Unit tests
├── fixtures/       # Test data and files
└── setup.js        # Test configuration
```

## 📊 Monitoring

### Health Checks

```bash
# Application health
curl http://localhost:3001/health

# System metrics
curl http://localhost:3001/metrics
```

### Logging

```bash
# View application logs
tail -f logs/app.log

# View error logs
tail -f logs/error.log

# Docker logs
docker-compose logs -f app
```

## 🛡️ Security

### Security Features

- **Rate Limiting**: Configurable per endpoint
- **Input Validation**: Comprehensive sanitization
- **Authentication**: JWT with refresh tokens
- **File Upload Security**: Type and size validation
- **CORS Protection**: Configurable origins
- **Security Headers**: Helmet.js implementation

### Security Checklist

- [ ] Strong passwords and secrets configured
- [ ] SSL/TLS certificates installed
- [ ] Firewall properly configured
- [ ] Database access secured
- [ ] Regular security updates applied
- [ ] Backup strategy implemented

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow ESLint configuration
- Write tests for new features
- Update documentation
- Ensure security best practices

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs](docs/) directory
- **Issues**: Report bugs via [GitHub Issues](https://github.com/your-org/spray-nft-backend/issues)
- **Discussions**: Join [GitHub Discussions](https://github.com/your-org/spray-nft-backend/discussions)

## 🙏 Acknowledgments

- [OpenZeppelin](https://openzeppelin.com/) for smart contract libraries
- [Polygon](https://polygon.technology/) for blockchain infrastructure
- [Pinata](https://pinata.cloud/) for IPFS services
- [Unity](https://unity.com/) for AR development platform

---

**Built with ❤️ for the Unity AR and NFT community**
