# SprayNFT Unity Integration

This directory contains Unity C# scripts and documentation for integrating with the SprayNFT backend system.

## Overview

The SprayNFT Unity integration allows developers to:
- Authenticate users with the backend system
- Upload AR artwork files and metadata to IPFS
- Mint NFTs from Unity-created artworks
- Manage user wallets and transactions
- Track NFT minting status and history

## Quick Start

1. **Copy Scripts**: Copy all C# scripts from the `Scripts/` directory to your Unity project's `Assets/Scripts/SprayNFT/` folder.

2. **Configure API**: Update the `SprayNFTConfig.cs` file with your backend API URL.

3. **Add to Scene**: Add the `SprayNFTManager` prefab to your scene or create a GameObject with the `SprayNFTManager` component.

4. **Initialize**: Call `SprayNFTManager.Instance.Initialize()` in your game's startup code.

## Architecture

### Core Components

- **SprayNFTManager**: Main singleton manager for all SprayNFT operations
- **SprayNFTAPI**: HTTP client for backend API communication
- **SprayNFTAuth**: User authentication and session management
- **SprayNFTArtwork**: Artwork upload and management
- **SprayNFTMinting**: NFT minting operations
- **SprayNFTWallet**: Wallet information and transaction tracking

### Data Models

- **User**: User account information and wallet details
- **Artwork**: Artwork metadata and IPFS information
- **NFT**: NFT token information and minting status
- **Transaction**: Blockchain transaction details

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/profile` - Get user profile

### Artwork Management
- `POST /api/artwork/upload` - Upload artwork files
- `GET /api/artwork` - Get user's artworks
- `GET /api/artwork/{id}` - Get specific artwork
- `PUT /api/artwork/{id}` - Update artwork metadata

### NFT Operations
- `POST /api/nft/mint` - Mint NFT from artwork
- `POST /api/nft/batch-mint` - Batch mint multiple NFTs
- `GET /api/nft/user-nfts` - Get user's NFTs
- `GET /api/nft/transaction-status/{hash}` - Get transaction status

### Wallet Management
- `GET /api/wallet/info` - Get wallet information
- `GET /api/wallet/transactions` - Get transaction history

## Usage Examples

### User Authentication

```csharp
// Register new user
var registerData = new RegisterRequest
{
    email = "<EMAIL>",
    username = "testuser",
    password = "SecurePassword123",
    firstName = "John",
    lastName = "Doe"
};

await SprayNFTManager.Instance.Auth.Register(registerData);

// Login user
var loginData = new LoginRequest
{
    email = "<EMAIL>",
    password = "SecurePassword123"
};

var loginResult = await SprayNFTManager.Instance.Auth.Login(loginData);
```

### Artwork Upload

```csharp
// Prepare artwork data
var artworkData = new ArtworkUploadRequest
{
    title = "My AR Creation",
    description = "Created in Unity AR",
    category = "3D",
    tags = new string[] { "AR", "Unity", "3D" }
};

// Upload artwork with files
var result = await SprayNFTManager.Instance.Artwork.UploadArtwork(
    artworkData,
    mainFilePath,
    thumbnailPath,
    additionalFiles
);
```

### NFT Minting

```csharp
// Mint NFT from artwork
var mintRequest = new MintNFTRequest
{
    to = userWalletAddress,
    artworkId = artworkId
};

var mintResult = await SprayNFTManager.Instance.Minting.MintNFT(mintRequest);

// Monitor transaction status
await SprayNFTManager.Instance.Minting.MonitorTransaction(mintResult.transactionHash);
```

## Error Handling

All API calls return a `SprayNFTResponse<T>` object with:
- `success`: Boolean indicating success/failure
- `data`: Response data (if successful)
- `message`: Success or error message
- `errors`: Detailed error information (if failed)

```csharp
var result = await SprayNFTManager.Instance.Auth.Login(loginData);
if (result.success)
{
    Debug.Log("Login successful!");
    // Handle success
}
else
{
    Debug.LogError($"Login failed: {result.message}");
    // Handle error
}
```

## Configuration

Update `SprayNFTConfig.cs` with your backend settings:

```csharp
public static class SprayNFTConfig
{
    public const string API_BASE_URL = "http://localhost:3001/api";
    public const int REQUEST_TIMEOUT = 30;
    public const bool ENABLE_LOGGING = true;
}
```

## File Upload Guidelines

### Supported File Types
- **Images**: JPEG, PNG, GIF, WebP (max 10MB)
- **3D Models**: GLB, GLTF (max 100MB)
- **Archives**: ZIP (max 200MB)

### File Preparation
1. Optimize file sizes for faster uploads
2. Use appropriate compression for images
3. Include thumbnails for 3D models
4. Organize additional files (textures, materials)

## Security Considerations

1. **API Keys**: Never hardcode API keys in client code
2. **Authentication**: Tokens are automatically managed by the SDK
3. **File Validation**: Files are validated on both client and server
4. **Rate Limiting**: API calls are automatically rate-limited

## Troubleshooting

### Common Issues

1. **Network Errors**: Check internet connection and API URL
2. **Authentication Failures**: Verify credentials and token expiry
3. **File Upload Errors**: Check file size and format restrictions
4. **Minting Failures**: Ensure sufficient wallet balance and valid artwork

### Debug Logging

Enable debug logging in `SprayNFTConfig.cs`:

```csharp
public const bool ENABLE_LOGGING = true;
```

### Support

For technical support and questions:
- Check the API documentation
- Review error messages and logs
- Contact the development team

## License

This integration is part of the SprayNFT project and follows the same license terms.
