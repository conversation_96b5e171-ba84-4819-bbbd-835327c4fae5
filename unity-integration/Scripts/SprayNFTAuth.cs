using System;
using UnityEngine;
using SprayNFT.Models;

namespace SprayNFT
{
    /// <summary>
    /// Handles user authentication and session management
    /// </summary>
    public class SprayNFTAuth : MonoBehaviour
    {
        [Header("Events")]
        public UnityEngine.Events.UnityEvent<User> OnUserLoggedIn;
        public UnityEngine.Events.UnityEvent OnUserLoggedOut;
        public UnityEngine.Events.UnityEvent<string> OnAuthError;

        private SprayNFTAPI api;
        private User currentUser;
        private bool isAuthenticated;

        /// <summary>
        /// Current authenticated user
        /// </summary>
        public User CurrentUser => currentUser;

        /// <summary>
        /// Whether user is authenticated
        /// </summary>
        public bool IsAuthenticated => isAuthenticated;

        /// <summary>
        /// Initialize authentication manager
        /// </summary>
        /// <param name="api">API client instance</param>
        public void Initialize(SprayNFTAPI api)
        {
            this.api = api;
            LoadStoredSession();
        }

        /// <summary>
        /// Register a new user
        /// </summary>
        /// <param name="registerData">Registration data</param>
        /// <param name="callback">Result callback</param>
        public void Register(RegisterRequest registerData, Action<SprayNFTResponse<AuthResponse>> callback = null)
        {
            SprayNFTConfig.Log($"Registering user: {registerData.email}");

            api.Post(SprayNFTConfig.Endpoints.REGISTER, registerData, (SprayNFTResponse<AuthResponse> response) =>
            {
                if (response.success && response.data != null)
                {
                    HandleAuthSuccess(response.data);
                    SprayNFTConfig.Log("User registered successfully");
                }
                else
                {
                    SprayNFTConfig.LogError($"Registration failed: {response.message}");
                    OnAuthError?.Invoke(response.message);
                }

                callback?.Invoke(response);
            }, false);
        }

        /// <summary>
        /// Login user
        /// </summary>
        /// <param name="loginData">Login credentials</param>
        /// <param name="callback">Result callback</param>
        public void Login(LoginRequest loginData, Action<SprayNFTResponse<AuthResponse>> callback = null)
        {
            SprayNFTConfig.Log($"Logging in user: {loginData.email}");

            api.Post(SprayNFTConfig.Endpoints.LOGIN, loginData, (SprayNFTResponse<AuthResponse> response) =>
            {
                if (response.success && response.data != null)
                {
                    HandleAuthSuccess(response.data);
                    SprayNFTConfig.Log("User logged in successfully");
                }
                else
                {
                    SprayNFTConfig.LogError($"Login failed: {response.message}");
                    OnAuthError?.Invoke(response.message);
                }

                callback?.Invoke(response);
            }, false);
        }

        /// <summary>
        /// Logout user
        /// </summary>
        /// <param name="callback">Result callback</param>
        public void Logout(Action<bool> callback = null)
        {
            SprayNFTConfig.Log("Logging out user");

            api.Post<object>(SprayNFTConfig.Endpoints.LOGOUT, null, (SprayNFTResponse<object> response) =>
            {
                // Clear session regardless of API response
                ClearSession();
                OnUserLoggedOut?.Invoke();
                
                SprayNFTConfig.Log("User logged out");
                callback?.Invoke(true);
            });
        }

        /// <summary>
        /// Get current user profile
        /// </summary>
        /// <param name="callback">Result callback</param>
        public void GetProfile(Action<SprayNFTResponse<User>> callback = null)
        {
            if (!isAuthenticated)
            {
                var errorResponse = new SprayNFTResponse<User>
                {
                    success = false,
                    message = "User not authenticated"
                };
                callback?.Invoke(errorResponse);
                return;
            }

            api.Get(SprayNFTConfig.Endpoints.PROFILE, (SprayNFTResponse<User> response) =>
            {
                if (response.success && response.data != null)
                {
                    currentUser = response.data;
                    SaveUserData();
                }

                callback?.Invoke(response);
            });
        }

        /// <summary>
        /// Update user profile
        /// </summary>
        /// <param name="profileData">Profile data to update</param>
        /// <param name="callback">Result callback</param>
        public void UpdateProfile(UserProfile profileData, Action<SprayNFTResponse<User>> callback = null)
        {
            if (!isAuthenticated)
            {
                var errorResponse = new SprayNFTResponse<User>
                {
                    success = false,
                    message = "User not authenticated"
                };
                callback?.Invoke(errorResponse);
                return;
            }

            api.Put(SprayNFTConfig.Endpoints.PROFILE, profileData, (SprayNFTResponse<User> response) =>
            {
                if (response.success && response.data != null)
                {
                    currentUser = response.data;
                    SaveUserData();
                    SprayNFTConfig.Log("Profile updated successfully");
                }

                callback?.Invoke(response);
            });
        }

        /// <summary>
        /// Refresh authentication token
        /// </summary>
        /// <param name="callback">Result callback</param>
        public void RefreshToken(Action<bool> callback = null)
        {
            api.RefreshAccessToken((SprayNFTResponse<AuthResponse> response) =>
            {
                bool success = false;
                
                if (response.success && response.data != null)
                {
                    // Update tokens but keep current user data
                    SaveTokens(response.data.token, response.data.refreshToken);
                    success = true;
                    SprayNFTConfig.Log("Token refreshed successfully");
                }
                else
                {
                    SprayNFTConfig.LogError("Token refresh failed");
                    ClearSession();
                    OnUserLoggedOut?.Invoke();
                }

                callback?.Invoke(success);
            });
        }

        /// <summary>
        /// Handle successful authentication
        /// </summary>
        private void HandleAuthSuccess(AuthResponse authData)
        {
            currentUser = authData.user;
            isAuthenticated = true;

            // Save session data
            SaveTokens(authData.token, authData.refreshToken);
            SaveUserData();
            SaveWalletAddress(authData.wallet.address);

            // Update API tokens
            api.SetTokens(authData.token, authData.refreshToken);

            // Trigger event
            OnUserLoggedIn?.Invoke(currentUser);
        }

        /// <summary>
        /// Clear user session
        /// </summary>
        private void ClearSession()
        {
            currentUser = null;
            isAuthenticated = false;

            // Clear stored data
            PlayerPrefs.DeleteKey(SprayNFTConfig.PlayerPrefsKeys.ACCESS_TOKEN);
            PlayerPrefs.DeleteKey(SprayNFTConfig.PlayerPrefsKeys.REFRESH_TOKEN);
            PlayerPrefs.DeleteKey(SprayNFTConfig.PlayerPrefsKeys.USER_DATA);
            PlayerPrefs.DeleteKey(SprayNFTConfig.PlayerPrefsKeys.WALLET_ADDRESS);
            PlayerPrefs.Save();

            // Clear API tokens
            api.ClearTokens();
        }

        /// <summary>
        /// Load stored session data
        /// </summary>
        private void LoadStoredSession()
        {
            string accessToken = PlayerPrefs.GetString(SprayNFTConfig.PlayerPrefsKeys.ACCESS_TOKEN, "");
            string refreshToken = PlayerPrefs.GetString(SprayNFTConfig.PlayerPrefsKeys.REFRESH_TOKEN, "");
            string userData = PlayerPrefs.GetString(SprayNFTConfig.PlayerPrefsKeys.USER_DATA, "");

            if (!string.IsNullOrEmpty(accessToken) && !string.IsNullOrEmpty(userData))
            {
                try
                {
                    currentUser = JsonUtility.FromJson<User>(userData);
                    isAuthenticated = true;
                    api.SetTokens(accessToken, refreshToken);
                    
                    SprayNFTConfig.Log("Loaded stored session");
                    OnUserLoggedIn?.Invoke(currentUser);
                }
                catch (Exception e)
                {
                    SprayNFTConfig.LogError($"Failed to load stored session: {e.Message}");
                    ClearSession();
                }
            }
        }

        /// <summary>
        /// Save authentication tokens
        /// </summary>
        private void SaveTokens(string accessToken, string refreshToken)
        {
            PlayerPrefs.SetString(SprayNFTConfig.PlayerPrefsKeys.ACCESS_TOKEN, accessToken);
            PlayerPrefs.SetString(SprayNFTConfig.PlayerPrefsKeys.REFRESH_TOKEN, refreshToken);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Save user data
        /// </summary>
        private void SaveUserData()
        {
            if (currentUser != null)
            {
                string userData = JsonUtility.ToJson(currentUser);
                PlayerPrefs.SetString(SprayNFTConfig.PlayerPrefsKeys.USER_DATA, userData);
                PlayerPrefs.Save();
            }
        }

        /// <summary>
        /// Save wallet address
        /// </summary>
        private void SaveWalletAddress(string walletAddress)
        {
            PlayerPrefs.SetString(SprayNFTConfig.PlayerPrefsKeys.WALLET_ADDRESS, walletAddress);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// Get stored wallet address
        /// </summary>
        public string GetWalletAddress()
        {
            return PlayerPrefs.GetString(SprayNFTConfig.PlayerPrefsKeys.WALLET_ADDRESS, "");
        }
    }
}
