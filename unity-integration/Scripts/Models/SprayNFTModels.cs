using System;
using System.Collections.Generic;
using UnityEngine;

namespace SprayNFT.Models
{
    /// <summary>
    /// Base response structure for all API calls
    /// </summary>
    [Serializable]
    public class SprayNFTResponse<T>
    {
        public bool success;
        public string message;
        public T data;
        public List<ApiError> errors;
    }

    /// <summary>
    /// API error information
    /// </summary>
    [Serializable]
    public class ApiError
    {
        public string field;
        public string message;
        public string code;
    }

    /// <summary>
    /// User registration request
    /// </summary>
    [Serializable]
    public class RegisterRequest
    {
        public string email;
        public string username;
        public string password;
        public string firstName;
        public string lastName;
    }

    /// <summary>
    /// User login request
    /// </summary>
    [Serializable]
    public class LoginRequest
    {
        public string email;
        public string password;
    }

    /// <summary>
    /// Authentication response
    /// </summary>
    [Serializable]
    public class AuthResponse
    {
        public User user;
        public string token;
        public string refreshToken;
        public WalletInfo wallet;
    }

    /// <summary>
    /// User information
    /// </summary>
    [Serializable]
    public class User
    {
        public string _id;
        public string email;
        public string username;
        public UserProfile profile;
        public UserStats stats;
        public bool isActive;
        public bool isEmailVerified;
        public string createdAt;
        public string updatedAt;
    }

    /// <summary>
    /// User profile information
    /// </summary>
    [Serializable]
    public class UserProfile
    {
        public string firstName;
        public string lastName;
        public string avatar;
        public string bio;
    }

    /// <summary>
    /// User statistics
    /// </summary>
    [Serializable]
    public class UserStats
    {
        public int totalMinted;
        public string totalSpent;
    }

    /// <summary>
    /// Wallet information
    /// </summary>
    [Serializable]
    public class WalletInfo
    {
        public string address;
        public WalletBalance balance;
        public int transactionCount;
        public NetworkInfo network;
    }

    /// <summary>
    /// Wallet balance information
    /// </summary>
    [Serializable]
    public class WalletBalance
    {
        public string wei;
        public string matic;
    }

    /// <summary>
    /// Network information
    /// </summary>
    [Serializable]
    public class NetworkInfo
    {
        public string name;
        public string chainId;
        public int blockNumber;
        public string gasPrice;
    }

    /// <summary>
    /// Artwork upload request
    /// </summary>
    [Serializable]
    public class ArtworkUploadRequest
    {
        public string title;
        public string description;
        public string category;
        public string[] tags;
        public ArtworkAttributes attributes;
        public UnityData unityData;
    }

    /// <summary>
    /// Artwork attributes
    /// </summary>
    [Serializable]
    public class ArtworkAttributes
    {
        public ArtworkDimensions dimensions;
        public string[] colors;
        public string style;
        public string medium;
        public string complexity;
    }

    /// <summary>
    /// Artwork dimensions
    /// </summary>
    [Serializable]
    public class ArtworkDimensions
    {
        public float width;
        public float height;
        public float depth;
    }

    /// <summary>
    /// Unity-specific data
    /// </summary>
    [Serializable]
    public class UnityData
    {
        public string sceneId;
        public int objectCount;
        public RenderSettings renderSettings;
        public ARSettings arSettings;
    }

    /// <summary>
    /// Unity render settings
    /// </summary>
    [Serializable]
    public class RenderSettings
    {
        public string lighting;
        public bool shadows;
        public bool postProcessing;
    }

    /// <summary>
    /// Unity AR settings
    /// </summary>
    [Serializable]
    public class ARSettings
    {
        public string trackingMode;
        public bool planeDetection;
        public bool lightEstimation;
    }

    /// <summary>
    /// Artwork information
    /// </summary>
    [Serializable]
    public class Artwork
    {
        public string _id;
        public string title;
        public string description;
        public string creator;
        public string category;
        public string status;
        public string visibility;
        public ArtworkFiles files;
        public ArtworkMetadata metadata;
        public ArtworkAttributes attributes;
        public UnityData unityData;
        public NFTInfo nft;
        public ArtworkStats stats;
        public string createdAt;
        public string updatedAt;
    }

    /// <summary>
    /// Artwork files information
    /// </summary>
    [Serializable]
    public class ArtworkFiles
    {
        public FileInfo main;
        public FileInfo thumbnail;
        public FileInfo[] additional;
    }

    /// <summary>
    /// File information
    /// </summary>
    [Serializable]
    public class FileInfo
    {
        public string originalName;
        public string mimeType;
        public long size;
        public string ipfsHash;
        public string ipfsUrl;
    }

    /// <summary>
    /// Artwork metadata
    /// </summary>
    [Serializable]
    public class ArtworkMetadata
    {
        public string ipfsHash;
        public string ipfsUrl;
        public MetadataContent content;
    }

    /// <summary>
    /// NFT metadata content
    /// </summary>
    [Serializable]
    public class MetadataContent
    {
        public string name;
        public string description;
        public string image;
        public string external_url;
        public MetadataAttribute[] attributes;
        public string animation_url;
        public string background_color;
    }

    /// <summary>
    /// NFT metadata attribute
    /// </summary>
    [Serializable]
    public class MetadataAttribute
    {
        public string trait_type;
        public string value;
        public string display_type;
    }

    /// <summary>
    /// NFT information
    /// </summary>
    [Serializable]
    public class NFTInfo
    {
        public bool isMinted;
        public string tokenId;
        public string contractAddress;
        public string transactionHash;
        public string mintedAt;
        public string mintedTo;
        public string network;
    }

    /// <summary>
    /// Artwork statistics
    /// </summary>
    [Serializable]
    public class ArtworkStats
    {
        public int views;
        public int downloads;
        public int likes;
        public int shares;
    }

    /// <summary>
    /// NFT mint request
    /// </summary>
    [Serializable]
    public class MintNFTRequest
    {
        public string to;
        public string artworkId;
        public string gasLimit;
        public string gasPrice;
        public string network;
    }

    /// <summary>
    /// Batch NFT mint request
    /// </summary>
    [Serializable]
    public class BatchMintNFTRequest
    {
        public string to;
        public string[] artworkIds;
        public string gasLimit;
        public string gasPrice;
        public string network;
    }

    /// <summary>
    /// NFT mint response
    /// </summary>
    [Serializable]
    public class MintNFTResponse
    {
        public string transactionHash;
        public Artwork artwork;
        public GasEstimate gasEstimate;
        public TransactionResult mintResult;
    }

    /// <summary>
    /// Gas estimation information
    /// </summary>
    [Serializable]
    public class GasEstimate
    {
        public string gasEstimate;
        public string gasLimit;
        public string gasPrice;
        public string maxFeePerGas;
        public string maxPriorityFeePerGas;
        public GasCost totalCost;
    }

    /// <summary>
    /// Gas cost information
    /// </summary>
    [Serializable]
    public class GasCost
    {
        public string wei;
        public string matic;
    }

    /// <summary>
    /// Transaction result
    /// </summary>
    [Serializable]
    public class TransactionResult
    {
        public string transactionHash;
        public string from;
        public string to;
        public string value;
        public string gasLimit;
        public string gasPrice;
        public int nonce;
        public string status;
    }

    /// <summary>
    /// Transaction status information
    /// </summary>
    [Serializable]
    public class TransactionStatus
    {
        public string transactionHash;
        public int blockNumber;
        public string blockHash;
        public int transactionIndex;
        public string from;
        public string to;
        public string gasUsed;
        public string gasPrice;
        public string status;
        public int confirmations;
        public TransactionEvent[] events;
    }

    /// <summary>
    /// Transaction event
    /// </summary>
    [Serializable]
    public class TransactionEvent
    {
        public string name;
        public string signature;
    }

    /// <summary>
    /// Contract information
    /// </summary>
    [Serializable]
    public class ContractInfo
    {
        public string address;
        public string name;
        public string symbol;
        public string totalSupply;
        public GasCost mintingFee;
        public string maxSupply;
        public string owner;
        public string network;
    }

    /// <summary>
    /// Pagination information
    /// </summary>
    [Serializable]
    public class Pagination
    {
        public int page;
        public int limit;
        public int total;
        public int pages;
    }

    /// <summary>
    /// Paginated response
    /// </summary>
    [Serializable]
    public class PaginatedResponse<T>
    {
        public T[] items;
        public Pagination pagination;
    }
}
