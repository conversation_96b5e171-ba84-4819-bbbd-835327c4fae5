using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;
using SprayNFT.Models;

namespace SprayNFT
{
    /// <summary>
    /// HTTP client for SprayNFT API communication
    /// </summary>
    public class SprayNFTAPI : MonoBehaviour
    {
        private string accessToken;
        private string refreshToken;

        /// <summary>
        /// Set authentication tokens
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <param name="refreshToken">Refresh token</param>
        public void SetTokens(string accessToken, string refreshToken)
        {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
        }

        /// <summary>
        /// Clear authentication tokens
        /// </summary>
        public void ClearTokens()
        {
            this.accessToken = null;
            this.refreshToken = null;
        }

        /// <summary>
        /// Make a GET request
        /// </summary>
        /// <typeparam name="T">Response data type</typeparam>
        /// <param name="endpoint">API endpoint</param>
        /// <param name="callback">Response callback</param>
        /// <param name="requireAuth">Whether authentication is required</param>
        public void Get<T>(string endpoint, Action<SprayNFTResponse<T>> callback, bool requireAuth = true)
        {
            StartCoroutine(MakeRequest<T>("GET", endpoint, null, callback, requireAuth));
        }

        /// <summary>
        /// Make a POST request
        /// </summary>
        /// <typeparam name="T">Response data type</typeparam>
        /// <param name="endpoint">API endpoint</param>
        /// <param name="data">Request data</param>
        /// <param name="callback">Response callback</param>
        /// <param name="requireAuth">Whether authentication is required</param>
        public void Post<T>(string endpoint, object data, Action<SprayNFTResponse<T>> callback, bool requireAuth = true)
        {
            StartCoroutine(MakeRequest<T>("POST", endpoint, data, callback, requireAuth));
        }

        /// <summary>
        /// Make a PUT request
        /// </summary>
        /// <typeparam name="T">Response data type</typeparam>
        /// <param name="endpoint">API endpoint</param>
        /// <param name="data">Request data</param>
        /// <param name="callback">Response callback</param>
        /// <param name="requireAuth">Whether authentication is required</param>
        public void Put<T>(string endpoint, object data, Action<SprayNFTResponse<T>> callback, bool requireAuth = true)
        {
            StartCoroutine(MakeRequest<T>("PUT", endpoint, data, callback, requireAuth));
        }

        /// <summary>
        /// Make a DELETE request
        /// </summary>
        /// <typeparam name="T">Response data type</typeparam>
        /// <param name="endpoint">API endpoint</param>
        /// <param name="callback">Response callback</param>
        /// <param name="requireAuth">Whether authentication is required</param>
        public void Delete<T>(string endpoint, Action<SprayNFTResponse<T>> callback, bool requireAuth = true)
        {
            StartCoroutine(MakeRequest<T>("DELETE", endpoint, null, callback, requireAuth));
        }

        /// <summary>
        /// Upload files with form data
        /// </summary>
        /// <typeparam name="T">Response data type</typeparam>
        /// <param name="endpoint">API endpoint</param>
        /// <param name="formData">Form data</param>
        /// <param name="callback">Response callback</param>
        public void UploadFiles<T>(string endpoint, List<IMultipartFormSection> formData, Action<SprayNFTResponse<T>> callback)
        {
            StartCoroutine(UploadFilesCoroutine<T>(endpoint, formData, callback));
        }

        /// <summary>
        /// Make HTTP request coroutine
        /// </summary>
        private IEnumerator MakeRequest<T>(string method, string endpoint, object data, Action<SprayNFTResponse<T>> callback, bool requireAuth)
        {
            string url = SprayNFTConfig.GetApiUrl(endpoint);
            UnityWebRequest request;

            // Create request based on method
            switch (method.ToUpper())
            {
                case "GET":
                    request = UnityWebRequest.Get(url);
                    break;
                case "POST":
                    request = CreateJsonRequest(url, method, data);
                    break;
                case "PUT":
                    request = CreateJsonRequest(url, method, data);
                    break;
                case "DELETE":
                    request = UnityWebRequest.Delete(url);
                    break;
                default:
                    SprayNFTConfig.LogError($"Unsupported HTTP method: {method}");
                    callback?.Invoke(CreateErrorResponse<T>("Unsupported HTTP method"));
                    yield break;
            }

            // Set headers
            SetRequestHeaders(request, requireAuth);

            // Set timeout
            request.timeout = SprayNFTConfig.REQUEST_TIMEOUT;

            SprayNFTConfig.Log($"{method} {url}");

            // Send request
            yield return request.SendWebRequest();

            // Handle response
            HandleResponse(request, callback);
        }

        /// <summary>
        /// Upload files coroutine
        /// </summary>
        private IEnumerator UploadFilesCoroutine<T>(string endpoint, List<IMultipartFormSection> formData, Action<SprayNFTResponse<T>> callback)
        {
            string url = SprayNFTConfig.GetApiUrl(endpoint);
            
            UnityWebRequest request = UnityWebRequest.Post(url, formData);
            SetRequestHeaders(request, true);
            request.timeout = SprayNFTConfig.REQUEST_TIMEOUT * 3; // Longer timeout for file uploads

            SprayNFTConfig.Log($"POST {url} (file upload)");

            yield return request.SendWebRequest();

            HandleResponse(request, callback);
        }

        /// <summary>
        /// Create JSON request
        /// </summary>
        private UnityWebRequest CreateJsonRequest(string url, string method, object data)
        {
            string jsonData = data != null ? JsonUtility.ToJson(data) : "{}";
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);

            UnityWebRequest request = new UnityWebRequest(url, method);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader(SprayNFTConfig.Headers.CONTENT_TYPE, SprayNFTConfig.ContentTypes.JSON);

            return request;
        }

        /// <summary>
        /// Set request headers
        /// </summary>
        private void SetRequestHeaders(UnityWebRequest request, bool requireAuth)
        {
            request.SetRequestHeader(SprayNFTConfig.Headers.USER_AGENT, SprayNFTConfig.UserAgent);

            if (requireAuth && !string.IsNullOrEmpty(accessToken))
            {
                request.SetRequestHeader(SprayNFTConfig.Headers.AUTHORIZATION, $"Bearer {accessToken}");
            }
        }

        /// <summary>
        /// Handle HTTP response
        /// </summary>
        private void HandleResponse<T>(UnityWebRequest request, Action<SprayNFTResponse<T>> callback)
        {
            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    string responseText = request.downloadHandler.text;
                    SprayNFTConfig.Log($"Response: {responseText}");

                    var response = JsonUtility.FromJson<SprayNFTResponse<T>>(responseText);
                    callback?.Invoke(response);
                }
                catch (Exception e)
                {
                    SprayNFTConfig.LogError($"Failed to parse response: {e.Message}");
                    callback?.Invoke(CreateErrorResponse<T>("Failed to parse response"));
                }
            }
            else
            {
                string errorMessage = $"HTTP Error {request.responseCode}: {request.error}";
                
                // Try to parse error response
                if (!string.IsNullOrEmpty(request.downloadHandler.text))
                {
                    try
                    {
                        var errorResponse = JsonUtility.FromJson<SprayNFTResponse<T>>(request.downloadHandler.text);
                        if (errorResponse != null && !string.IsNullOrEmpty(errorResponse.message))
                        {
                            errorMessage = errorResponse.message;
                        }
                        callback?.Invoke(errorResponse);
                        return;
                    }
                    catch
                    {
                        // Fall back to generic error
                    }
                }

                SprayNFTConfig.LogError(errorMessage);
                callback?.Invoke(CreateErrorResponse<T>(errorMessage));
            }
        }

        /// <summary>
        /// Create error response
        /// </summary>
        private SprayNFTResponse<T> CreateErrorResponse<T>(string message)
        {
            return new SprayNFTResponse<T>
            {
                success = false,
                message = message,
                data = default(T),
                errors = new List<ApiError>
                {
                    new ApiError { message = message }
                }
            };
        }

        /// <summary>
        /// Refresh access token
        /// </summary>
        /// <param name="callback">Callback with new tokens</param>
        public void RefreshAccessToken(Action<SprayNFTResponse<AuthResponse>> callback)
        {
            if (string.IsNullOrEmpty(refreshToken))
            {
                callback?.Invoke(CreateErrorResponse<AuthResponse>("No refresh token available"));
                return;
            }

            var refreshData = new { refreshToken = this.refreshToken };
            Post(SprayNFTConfig.Endpoints.REFRESH, refreshData, (SprayNFTResponse<AuthResponse> response) =>
            {
                if (response.success && response.data != null)
                {
                    SetTokens(response.data.token, response.data.refreshToken);
                }
                callback?.Invoke(response);
            }, false);
        }
    }
}
