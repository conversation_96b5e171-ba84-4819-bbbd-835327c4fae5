using UnityEngine;

namespace SprayNFT
{
    /// <summary>
    /// Configuration settings for SprayNFT integration
    /// </summary>
    public static class SprayNFTConfig
    {
        /// <summary>
        /// Base URL for the SprayNFT API
        /// Update this to match your backend deployment
        /// </summary>
        public const string API_BASE_URL = "http://localhost:3001/api";
        
        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public const int REQUEST_TIMEOUT = 30;
        
        /// <summary>
        /// Enable debug logging
        /// </summary>
        public const bool ENABLE_LOGGING = true;
        
        /// <summary>
        /// Maximum file size for uploads (in bytes)
        /// </summary>
        public const long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
        
        /// <summary>
        /// Supported image file extensions
        /// </summary>
        public static readonly string[] SUPPORTED_IMAGE_EXTENSIONS = 
        {
            ".jpg", ".jpeg", ".png", ".gif", ".webp"
        };
        
        /// <summary>
        /// Supported 3D model file extensions
        /// </summary>
        public static readonly string[] SUPPORTED_MODEL_EXTENSIONS = 
        {
            ".glb", ".gltf"
        };
        
        /// <summary>
        /// Supported archive file extensions
        /// </summary>
        public static readonly string[] SUPPORTED_ARCHIVE_EXTENSIONS = 
        {
            ".zip"
        };
        
        /// <summary>
        /// Default network for blockchain operations
        /// </summary>
        public const string DEFAULT_NETWORK = "amoy";
        
        /// <summary>
        /// Polling interval for transaction monitoring (in seconds)
        /// </summary>
        public const float TRANSACTION_POLL_INTERVAL = 5f;
        
        /// <summary>
        /// Maximum time to wait for transaction confirmation (in seconds)
        /// </summary>
        public const float TRANSACTION_TIMEOUT = 300f; // 5 minutes
        
        /// <summary>
        /// PlayerPrefs keys for persistent storage
        /// </summary>
        public static class PlayerPrefsKeys
        {
            public const string ACCESS_TOKEN = "SprayNFT_AccessToken";
            public const string REFRESH_TOKEN = "SprayNFT_RefreshToken";
            public const string USER_DATA = "SprayNFT_UserData";
            public const string WALLET_ADDRESS = "SprayNFT_WalletAddress";
        }
        
        /// <summary>
        /// API endpoint paths
        /// </summary>
        public static class Endpoints
        {
            // Authentication
            public const string REGISTER = "/auth/register";
            public const string LOGIN = "/auth/login";
            public const string REFRESH = "/auth/refresh";
            public const string LOGOUT = "/auth/logout";
            public const string PROFILE = "/auth/profile";
            
            // Artwork
            public const string ARTWORK_UPLOAD = "/artwork/upload";
            public const string ARTWORK_LIST = "/artwork";
            public const string ARTWORK_DETAIL = "/artwork/{0}";
            public const string ARTWORK_UPDATE = "/artwork/{0}";
            public const string ARTWORK_DELETE = "/artwork/{0}";
            public const string ARTWORK_STATS = "/artwork/stats";
            
            // NFT
            public const string NFT_MINT = "/nft/mint";
            public const string NFT_BATCH_MINT = "/nft/batch-mint";
            public const string NFT_ESTIMATE_GAS = "/nft/estimate-gas";
            public const string NFT_TRANSACTION_STATUS = "/nft/transaction-status/{0}";
            public const string NFT_USER_NFTS = "/nft/user-nfts";
            public const string NFT_CONTRACT_INFO = "/nft/contract-info";
            
            // Wallet
            public const string WALLET_INFO = "/wallet/info";
            public const string WALLET_TRANSACTIONS = "/wallet/transactions";
            public const string WALLET_NETWORK_STATUS = "/wallet/network-status";
        }
        
        /// <summary>
        /// HTTP headers
        /// </summary>
        public static class Headers
        {
            public const string AUTHORIZATION = "Authorization";
            public const string CONTENT_TYPE = "Content-Type";
            public const string USER_AGENT = "User-Agent";
        }
        
        /// <summary>
        /// Content types
        /// </summary>
        public static class ContentTypes
        {
            public const string JSON = "application/json";
            public const string FORM_DATA = "multipart/form-data";
        }
        
        /// <summary>
        /// User agent string for API requests
        /// </summary>
        public static string UserAgent => $"SprayNFT-Unity/{Application.version} (Unity {Application.unityVersion})";
        
        /// <summary>
        /// Get full API URL for an endpoint
        /// </summary>
        /// <param name="endpoint">Endpoint path</param>
        /// <returns>Full URL</returns>
        public static string GetApiUrl(string endpoint)
        {
            return API_BASE_URL + endpoint;
        }
        
        /// <summary>
        /// Check if a file extension is supported
        /// </summary>
        /// <param name="extension">File extension (with dot)</param>
        /// <returns>True if supported</returns>
        public static bool IsFileExtensionSupported(string extension)
        {
            extension = extension.ToLower();
            
            foreach (var supportedExt in SUPPORTED_IMAGE_EXTENSIONS)
            {
                if (extension == supportedExt) return true;
            }
            
            foreach (var supportedExt in SUPPORTED_MODEL_EXTENSIONS)
            {
                if (extension == supportedExt) return true;
            }
            
            foreach (var supportedExt in SUPPORTED_ARCHIVE_EXTENSIONS)
            {
                if (extension == supportedExt) return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// Log debug message if logging is enabled
        /// </summary>
        /// <param name="message">Message to log</param>
        public static void Log(string message)
        {
            if (ENABLE_LOGGING)
            {
                Debug.Log($"[SprayNFT] {message}");
            }
        }
        
        /// <summary>
        /// Log warning message if logging is enabled
        /// </summary>
        /// <param name="message">Message to log</param>
        public static void LogWarning(string message)
        {
            if (ENABLE_LOGGING)
            {
                Debug.LogWarning($"[SprayNFT] {message}");
            }
        }
        
        /// <summary>
        /// Log error message if logging is enabled
        /// </summary>
        /// <param name="message">Message to log</param>
        public static void LogError(string message)
        {
            if (ENABLE_LOGGING)
            {
                Debug.LogError($"[SprayNFT] {message}");
            }
        }
    }
}
