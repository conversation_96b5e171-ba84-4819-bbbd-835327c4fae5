# SprayNFT Backend Deployment Guide

This guide covers the complete deployment process for the SprayNFT backend system in production environments.

## Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores
- **Network**: Stable internet connection

### Software Requirements
- **Node.js**: v18.x or higher
- **npm**: v9.x or higher
- **Docker**: v20.x or higher (optional)
- **Docker Compose**: v2.x or higher (optional)
- **MongoDB**: v6.x or higher
- **Nginx**: v1.20+ (for reverse proxy)

## Environment Setup

### 1. Clone Repository
```bash
git clone https://github.com/your-org/spray-nft-backend.git
cd spray-nft-backend
```

### 2. Environment Variables
Create a `.env` file with the following variables:

```bash
# Application
NODE_ENV=production
PORT=3001

# Database
MONGODB_URI=****************************************************

# JWT Secrets (generate strong secrets)
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here

# IPFS/Pinata Configuration
PINATA_API_KEY=your-pinata-api-key
PINATA_SECRET_KEY=your-pinata-secret-key

# Blockchain Configuration
POLYGON_RPC_URL=https://polygon-rpc.com
MUMBAI_RPC_URL=https://rpc.ankr.com/polygon_amoy
PRIVATE_KEY=your-deployer-private-key-here

# Smart Contract Addresses (set after deployment)
NFT_CONTRACT_ADDRESS_POLYGON=0x...
NFT_CONTRACT_ADDRESS_MUMBAI=0x...

# Security
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
REQUIRE_API_KEY=false
IP_WHITELIST=

# Logging
LOG_LEVEL=info
```

### 3. Generate Secure Keys
```bash
# Generate JWT secrets
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# Generate encryption key (32 characters)
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
```

## Deployment Methods

### Method 1: Docker Deployment (Recommended)

#### 1. Build and Start Services
```bash
# Copy environment file
cp .env.example .env
# Edit .env with your configuration

# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

#### 2. Deploy Smart Contracts
```bash
# Deploy to testnet first
docker-compose exec app npm run deploy:testnet

# Deploy to mainnet (after testing)
docker-compose exec app npm run deploy:mainnet
```

#### 3. Verify Deployment
```bash
# Check application health
curl http://localhost/health

# Check logs
docker-compose logs -f app
```

### Method 2: Manual Deployment

#### 1. Install Dependencies
```bash
npm ci --only=production
```

#### 2. Setup Database
```bash
# Install MongoDB
sudo apt update
sudo apt install -y mongodb

# Start MongoDB service
sudo systemctl start mongodb
sudo systemctl enable mongodb

# Create database user
mongo
> use spraynft
> db.createUser({
    user: "spraynft",
    pwd: "secure-password",
    roles: ["readWrite"]
  })
```

#### 3. Compile Smart Contracts
```bash
npm run compile
```

#### 4. Deploy Smart Contracts
```bash
# Deploy to testnet
npm run deploy:testnet

# Deploy to mainnet
npm run deploy:mainnet
```

#### 5. Start Application
```bash
# Using PM2 (recommended)
npm install -g pm2
pm2 start src/server.js --name spraynft-backend
pm2 startup
pm2 save

# Or using systemd
sudo cp scripts/spraynft.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable spraynft
sudo systemctl start spraynft
```

#### 6. Setup Nginx Reverse Proxy
```bash
# Install Nginx
sudo apt install nginx

# Copy configuration
sudo cp nginx/nginx.conf /etc/nginx/sites-available/spraynft
sudo ln -s /etc/nginx/sites-available/spraynft /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## SSL/TLS Configuration

### 1. Obtain SSL Certificate
```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

### 2. Update Nginx Configuration
Uncomment the HTTPS server block in `nginx/nginx.conf` and update with your domain.

## Monitoring Setup

### 1. Application Monitoring
The application includes built-in monitoring endpoints:
- Health check: `GET /health`
- Metrics: `GET /metrics`

### 2. Log Monitoring
```bash
# View application logs
tail -f logs/app.log

# View error logs
tail -f logs/error.log

# View access logs
tail -f /var/log/nginx/access.log
```

### 3. System Monitoring
```bash
# Monitor system resources
htop

# Monitor disk usage
df -h

# Monitor network connections
netstat -tulpn
```

## Security Checklist

### Application Security
- [ ] Strong JWT secrets configured
- [ ] Database credentials secured
- [ ] Private keys encrypted and secured
- [ ] CORS origins properly configured
- [ ] Rate limiting enabled
- [ ] Input validation implemented
- [ ] File upload restrictions in place

### Infrastructure Security
- [ ] Firewall configured (only necessary ports open)
- [ ] SSH key-based authentication
- [ ] Regular security updates applied
- [ ] SSL/TLS certificates installed
- [ ] Database access restricted
- [ ] Backup strategy implemented

### Blockchain Security
- [ ] Private keys stored securely
- [ ] Smart contracts audited
- [ ] Gas limits configured appropriately
- [ ] Network endpoints secured

## Backup Strategy

### 1. Database Backup
```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --db spraynft --out /backups/mongodb_$DATE
tar -czf /backups/mongodb_$DATE.tar.gz /backups/mongodb_$DATE
rm -rf /backups/mongodb_$DATE

# Schedule with cron
0 2 * * * /path/to/backup-script.sh
```

### 2. File Backup
```bash
# Backup uploaded files
rsync -av uploads/ /backups/uploads/

# Backup logs
rsync -av logs/ /backups/logs/
```

### 3. Configuration Backup
```bash
# Backup environment and configuration
cp .env /backups/env_backup
cp -r nginx/ /backups/nginx_backup
```

## Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check logs
docker-compose logs app
# or
pm2 logs spraynft-backend

# Check environment variables
env | grep -E "(NODE_ENV|MONGODB_URI|JWT_SECRET)"

# Check port availability
netstat -tulpn | grep 3001
```

#### Database Connection Issues
```bash
# Test MongoDB connection
mongo $MONGODB_URI

# Check MongoDB status
sudo systemctl status mongodb

# Check network connectivity
telnet mongodb-host 27017
```

#### Smart Contract Deployment Issues
```bash
# Check network connectivity
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  $POLYGON_RPC_URL

# Check account balance
npm run check-balance

# Verify private key format
node -e "console.log(require('ethers').Wallet.fromPrivateKey('$PRIVATE_KEY').address)"
```

#### Performance Issues
```bash
# Monitor system resources
htop
iostat -x 1

# Check application metrics
curl http://localhost:3001/metrics

# Analyze slow queries
# Enable MongoDB profiling and check slow operations
```

## Maintenance

### Regular Tasks
- Monitor application logs daily
- Check system resources weekly
- Update dependencies monthly
- Review security logs weekly
- Test backup restoration quarterly

### Updates
```bash
# Update application
git pull origin main
npm ci --only=production
pm2 restart spraynft-backend

# Update system packages
sudo apt update && sudo apt upgrade
```

## Support

For technical support:
1. Check application logs first
2. Review this deployment guide
3. Check the troubleshooting section
4. Contact the development team with:
   - Error messages
   - Log excerpts
   - System information
   - Steps to reproduce the issue

## Performance Optimization

### Database Optimization
- Enable MongoDB indexing
- Configure connection pooling
- Monitor query performance
- Implement data archiving

### Application Optimization
- Enable response compression
- Implement caching strategies
- Optimize file upload handling
- Configure proper logging levels

### Infrastructure Optimization
- Use CDN for static assets
- Implement load balancing for high traffic
- Configure auto-scaling
- Monitor and optimize resource usage
