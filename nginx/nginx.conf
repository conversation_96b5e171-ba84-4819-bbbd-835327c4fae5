user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/m;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.pinata.cloud https://*.infura.io; font-src 'self'; object-src 'none'; media-src 'self'; frame-src 'none';" always;

    # Upstream backend
    upstream spraynft_backend {
        server app:3001;
        keepalive 32;
    }

    # HTTP server (redirect to HTTPS in production)
    server {
        listen 80;
        server_name _;

        # Health check endpoint
        location /health {
            proxy_pass http://spraynft_backend/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Redirect all other traffic to HTTPS (uncomment for production)
        # return 301 https://$server_name$request_uri;

        # For development, proxy all requests
        location / {
            proxy_pass http://spraynft_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }
    }

    # HTTPS server (uncomment and configure for production)
    # server {
    #     listen 443 ssl http2;
    #     server_name yourdomain.com;
    #
    #     # SSL configuration
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     ssl_session_cache shared:SSL:10m;
    #     ssl_session_timeout 10m;
    #
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    #
    #     # API endpoints with rate limiting
    #     location /api/auth/ {
    #         limit_req zone=auth burst=10 nodelay;
    #         proxy_pass http://spraynft_backend;
    #         include /etc/nginx/proxy_params;
    #     }
    #
    #     location /api/artwork/upload {
    #         limit_req zone=upload burst=5 nodelay;
    #         client_max_body_size 100M;
    #         proxy_pass http://spraynft_backend;
    #         include /etc/nginx/proxy_params;
    #         proxy_read_timeout 300s;
    #     }
    #
    #     location /api/ {
    #         limit_req zone=api burst=20 nodelay;
    #         proxy_pass http://spraynft_backend;
    #         include /etc/nginx/proxy_params;
    #     }
    #
    #     # Health check
    #     location /health {
    #         proxy_pass http://spraynft_backend/health;
    #         include /etc/nginx/proxy_params;
    #     }
    #
    #     # Metrics (restrict access)
    #     location /metrics {
    #         allow 127.0.0.1;
    #         allow 10.0.0.0/8;
    #         deny all;
    #         proxy_pass http://spraynft_backend/metrics;
    #         include /etc/nginx/proxy_params;
    #     }
    #
    #     # Static files (if any)
    #     location /static/ {
    #         alias /app/static/;
    #         expires 1y;
    #         add_header Cache-Control "public, immutable";
    #     }
    # }

    # Proxy parameters file
    # Create /etc/nginx/proxy_params with:
    # proxy_http_version 1.1;
    # proxy_set_header Upgrade $http_upgrade;
    # proxy_set_header Connection 'upgrade';
    # proxy_set_header Host $host;
    # proxy_set_header X-Real-IP $remote_addr;
    # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    # proxy_set_header X-Forwarded-Proto $scheme;
    # proxy_cache_bypass $http_upgrade;
    # proxy_read_timeout 300s;
    # proxy_connect_timeout 75s;
}
