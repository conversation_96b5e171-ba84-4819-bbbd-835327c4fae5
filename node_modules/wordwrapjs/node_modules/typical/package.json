{"name": "typical", "author": "<PERSON> <<EMAIL>>", "version": "5.2.0", "description": "Isomorphic, functional type-checking for Javascript", "repository": "https://github.com/75lb/typical", "license": "MIT", "main": "dist/index.js", "keywords": ["type", "checking", "check", "value", "valid", "is", "number", "object", "plainobject", "array", "like", "defined", "string", "boolean", "function", "promise", "iterable", "class", "primitive", "is<PERSON>ring", "isclass", "isiterable", "isdefined", "isobject", "isomorphic"], "engines": {"node": ">=8"}, "scripts": {"test": "npm run dist && npm run test:js && npm run test:esm", "test:all": "npm run test:js && npm run test:esm && npm run test:web", "test:js": "rollup test/*.mjs -f cjs -d tmp/test -e assert && test-runner tmp/test/test*.js", "test:esm": "esm-runner test/*.mjs", "test:web": "web-runner test/test.mjs", "test:v8": "rollup test/test.mjs test/test-default.mjs -f cjs -d tmp/testv8 && test-runner tmp/testv8/test*.js", "dist": "rollup index.mjs -f umd -n typical -o dist/index.js --exports named", "docs": "jsdoc2md -c jsdoc.conf -t README.hbs index.mjs > README.md; echo", "cover": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "devDependencies": {"coveralls": "^3.0.7", "esm-runner": "^0.1.5", "jsdoc-to-markdown": "^5.0.2", "nyc": "^14.1.1", "rollup": "^1.25.1", "test-object-model": "^0.4.4", "test-runner": "^0.6.0"}, "files": ["index.mjs", "dist/index.js"]}