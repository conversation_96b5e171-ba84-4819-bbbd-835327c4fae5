T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
T__11=12
T__12=13
T__13=14
T__14=15
T__15=16
T__16=17
T__17=18
T__18=19
T__19=20
T__20=21
T__21=22
T__22=23
T__23=24
T__24=25
T__25=26
T__26=27
T__27=28
T__28=29
T__29=30
T__30=31
T__31=32
T__32=33
T__33=34
T__34=35
T__35=36
T__36=37
T__37=38
T__38=39
T__39=40
T__40=41
T__41=42
T__42=43
T__43=44
T__44=45
T__45=46
T__46=47
T__47=48
T__48=49
T__49=50
T__50=51
T__51=52
T__52=53
T__53=54
T__54=55
T__55=56
T__56=57
T__57=58
T__58=59
T__59=60
T__60=61
T__61=62
T__62=63
T__63=64
T__64=65
T__65=66
T__66=67
T__67=68
T__68=69
T__69=70
T__70=71
T__71=72
T__72=73
T__73=74
T__74=75
T__75=76
T__76=77
T__77=78
T__78=79
T__79=80
T__80=81
T__81=82
T__82=83
T__83=84
T__84=85
T__85=86
T__86=87
T__87=88
T__88=89
T__89=90
T__90=91
T__91=92
T__92=93
T__93=94
T__94=95
T__95=96
T__96=97
T__97=98
Int=99
Uint=100
Byte=101
Fixed=102
Ufixed=103
BooleanLiteral=104
DecimalNumber=105
HexNumber=106
NumberUnit=107
HexLiteralFragment=108
ReservedKeyword=109
AnonymousKeyword=110
BreakKeyword=111
ConstantKeyword=112
TransientKeyword=113
ImmutableKeyword=114
ContinueKeyword=115
LeaveKeyword=116
ExternalKeyword=117
IndexedKeyword=118
InternalKeyword=119
PayableKeyword=120
PrivateKeyword=121
PublicKeyword=122
VirtualKeyword=123
PureKeyword=124
TypeKeyword=125
ViewKeyword=126
GlobalKeyword=127
ConstructorKeyword=128
FallbackKeyword=129
ReceiveKeyword=130
Identifier=131
StringLiteralFragment=132
VersionLiteral=133
WS=134
COMMENT=135
LINE_COMMENT=136
'pragma'=1
';'=2
'*'=3
'||'=4
'^'=5
'~'=6
'>='=7
'>'=8
'<'=9
'<='=10
'='=11
'as'=12
'import'=13
'from'=14
'{'=15
','=16
'}'=17
'abstract'=18
'contract'=19
'interface'=20
'library'=21
'is'=22
'('=23
')'=24
'layout'=25
'at'=26
'error'=27
'using'=28
'for'=29
'|'=30
'&'=31
'+'=32
'-'=33
'/'=34
'%'=35
'=='=36
'!='=37
'struct'=38
'modifier'=39
'function'=40
'returns'=41
'event'=42
'enum'=43
'['=44
']'=45
'address'=46
'.'=47
'mapping'=48
'=>'=49
'memory'=50
'storage'=51
'calldata'=52
'if'=53
'else'=54
'try'=55
'catch'=56
'while'=57
'unchecked'=58
'assembly'=59
'do'=60
'return'=61
'throw'=62
'emit'=63
'revert'=64
'var'=65
'bool'=66
'string'=67
'byte'=68
'++'=69
'--'=70
'new'=71
':'=72
'delete'=73
'!'=74
'**'=75
'<<'=76
'>>'=77
'&&'=78
'?'=79
'|='=80
'^='=81
'&='=82
'<<='=83
'>>='=84
'+='=85
'-='=86
'*='=87
'/='=88
'%='=89
'let'=90
':='=91
'=:'=92
'switch'=93
'case'=94
'default'=95
'->'=96
'callback'=97
'override'=98
'anonymous'=110
'break'=111
'constant'=112
'transient'=113
'immutable'=114
'continue'=115
'leave'=116
'external'=117
'indexed'=118
'internal'=119
'payable'=120
'private'=121
'public'=122
'virtual'=123
'pure'=124
'type'=125
'view'=126
'global'=127
'constructor'=128
'fallback'=129
'receive'=130
