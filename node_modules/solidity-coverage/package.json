{"name": "solidity-coverage", "version": "0.8.16", "description": "Code coverage for Solidity testing", "main": "plugins/nomiclabs.plugin.js", "bin": {"solidity-coverage": "./plugins/bin.js"}, "directories": {"test": "test"}, "scripts": {"test:unit": "./scripts/unit.sh", "test:integration": "./scripts/integration.sh", "test:ci": "./scripts/ci.sh", "test:unit:viaIR": "VIA_IR=true ./scripts/unit.sh", "test:integration:viaIR": "VIA_IR=true ./scripts/integration.sh", "test:ci:viaIR": "VIA_IR=true ./scripts/ci.sh"}, "homepage": "https://github.com/sc-forks/solidity-coverage", "repository": {"type": "git", "url": "https://github.com/sc-forks/solidity-coverage.git"}, "author": "", "license": "ISC", "dependencies": {"@ethersproject/abi": "^5.0.9", "@solidity-parser/parser": "^0.20.1", "chalk": "^2.4.2", "death": "^1.1.0", "difflib": "^0.2.4", "fs-extra": "^8.1.0", "ghost-testrpc": "^0.0.2", "global-modules": "^2.0.0", "globby": "^10.0.1", "jsonschema": "^1.2.4", "lodash": "^4.17.21", "mocha": "^10.2.0", "node-emoji": "^1.10.0", "pify": "^4.0.1", "recursive-readdir": "^2.2.2", "sc-istanbul": "^0.4.5", "semver": "^7.3.4", "shelljs": "^0.8.3", "web3-utils": "^1.3.6"}, "devDependencies": {"@nomicfoundation/hardhat-network-helpers": "^1.0.10", "@nomicfoundation/hardhat-viem": "^2.0.0", "@nomiclabs/hardhat-ethers": "^2.0.4", "@nomiclabs/hardhat-truffle5": "^2.0.0", "@nomiclabs/hardhat-waffle": "^2.0.1", "@nomiclabs/hardhat-web3": "^2.0.0", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "decache": "^4.5.1", "ethereum-waffle": "^3.4.0", "ethers": "^5.5.3", "hardhat": "^2.22.2", "hardhat-gas-reporter": "^1.0.1", "nyc": "^14.1.1", "solc": "0.8.24", "viem": "^2.9.9"}, "peerDependencies": {"hardhat": "^2.11.0"}}