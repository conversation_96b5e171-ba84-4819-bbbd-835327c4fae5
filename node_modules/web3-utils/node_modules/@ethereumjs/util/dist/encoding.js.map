{"version": 3, "file": "encoding.js", "sourceRoot": "", "sources": ["../src/encoding.ts"], "names": [], "mappings": ";;;AAAA,wGAAwG;AACxG;;;;;GAKG;AACI,MAAM,aAAa,GAAG,CAAC,OAAmB,EAAE,EAAE;IACnD,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAA;AACjE,CAAC,CAAA;AAFY,QAAA,aAAa,iBAEzB;AAEM,MAAM,cAAc,GAAG,CAAC,OAAmB,EAAE,KAAiB,EAAE,EAAE;IACvE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QAC9D,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;KACjD;AACH,CAAC,CAAA;AAJY,QAAA,cAAc,kBAI1B;AAEM,MAAM,qBAAqB,GAAG,CAAC,OAAmB,EAAE,EAAE;IAC3D,IAAI,UAAU,GAAG,CAAC,CAAA;IAClB,IAAI,IAAA,qBAAa,EAAC,OAAO,CAAC,EAAE;QAC1B,UAAU,GAAG,CAAC,CAAA;QACd,0CAA0C;QAC1C,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;KAClD;IACD,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;IAClD,4DAA4D;IAC5D,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAA;IACxB,oFAAoF;IACpF,qEAAqE;IACrE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC9B,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChB,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAA;QACpB,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;KAC9B;IACD,4CAA4C;IAC5C,IAAA,sBAAc,EAAC,OAAO,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IACxC,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AApBY,QAAA,qBAAqB,yBAoBjC;AAEM,MAAM,cAAc,GAAG,CAAC,GAAe,EAAE,EAAE;IAChD,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5B,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAA;IACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QAChB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;QACvB,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;KAC5B;IACD,kEAAkE;IAClE,2CAA2C;IAC3C,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAA;IACnB,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAZY,QAAA,cAAc,kBAY1B;AAEM,MAAM,qBAAqB,GAAG,CAAC,OAAmB,EAAE,EAAE;IAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO,OAAO,CAAA;KACf;IACD,IAAI,IAAI,GAAG,IAAA,sBAAc,EAAC,OAAO,CAAC,CAAA;IAClC,oEAAoE;IACpE,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;QACf,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;KACzC;IACD,oEAAoE;IACpE,sDAAsD;IACtD,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;AAC5B,CAAC,CAAA;AAbY,QAAA,qBAAqB,yBAajC;AAED;;;;;;GAMG;AACH,EAAE;AACF,EAAE;AACF,iEAAiE;AACjE,kEAAkE;AAClE,sGAAsG;AACtG,+FAA+F;AAC/F,0CAA0C;AAC1C,2DAA2D;AAC3D,uCAAuC;AACvC,IAAI"}