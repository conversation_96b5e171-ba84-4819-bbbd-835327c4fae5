{"name": "@types/node", "version": "8.10.66", "description": "TypeScript definitions for Node.js", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft", "githubUsername": "Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped", "githubUsername": "DefinitelyTyped"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs", "githubUsername": "parambirs"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker", "githubUsername": "WilcoBakker"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89", "githubUsername": "smac89"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Flarna", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3", "githubUsername": "wwwy3y3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas", "githubUsername": "DeividasBakanas"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin", "githubUsername": "kjin"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis", "githubUsername": "alvis"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK", "githubUsername": "Hannes-<PERSON><PERSON>-CK"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno", "githubUsername": "jkomyno"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29", "githubUsername": "hoo29"}, {"name": "<PERSON>", "url": "https://github.com/n-e", "githubUsername": "n-e"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin", "githubUsername": "galkin"}, {"name": "<PERSON>", "url": "https://github.com/bruno<PERSON>ufler", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/KSXGitHub", "githubUsername": "KSXGitHub"}, {"name": "Lishude", "url": "https://github.com/islishude", "githubUsername": "islishude"}, {"name": "<PERSON>", "url": "https://github.com/r3nya", "githubUsername": "r3nya"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/j-oliveras", "githubUsername": "j-oliveras"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy", "githubUsername": "b<PERSON>y"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "bbdc3f41bff51f7e23469a2e9fc3ee831a778f6bea4de0a8f85e86b868dd7bd3", "typeScriptVersion": "3.2"}