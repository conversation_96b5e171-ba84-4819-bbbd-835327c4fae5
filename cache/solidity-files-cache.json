{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Documents/sprayNft/contracts/SprayNFT.sol": {"lastModificationDate": 1752011418631, "contentHash": "58532bede9221a069a4dcfb0ff6818e1", "sourceName": "contracts/SprayNFT.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC721/ERC721.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol", "@openzeppelin/contracts/interfaces/IERC2981.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SprayNFT"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1751998865252, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"lastModificationDate": 1751998865259, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/Pausable.sol": {"lastModificationDate": 1751998865255, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol": {"lastModificationDate": 1751998865197, "contentHash": "594379619f21d2767c325a6c46b53399", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Burnable"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol": {"lastModificationDate": 1751998865201, "contentHash": "9aefce4573c7ae661acf9d5d78d5450b", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "../../../utils/Strings.sol", "../../../interfaces/IERC4906.sol", "../../../interfaces/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721URIStorage"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/token/ERC721/ERC721.sol": {"lastModificationDate": 1751998865195, "contentHash": "a7951c81f7037d558c6d2f220b0cb38e", "sourceName": "@openzeppelin/contracts/token/ERC721/ERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./IERC721.sol", "./extensions/IERC721Metadata.sol", "./utils/ERC721Utils.sol", "../../utils/Context.sol", "../../utils/Strings.sol", "../../utils/introspection/ERC165.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/interfaces/IERC2981.sol": {"lastModificationDate": 1751998865235, "contentHash": "1f306be7759a59a417eae412865daa78", "sourceName": "@openzeppelin/contracts/interfaces/IERC2981.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC2981"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1751998865122, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/Strings.sol": {"lastModificationDate": 1751998865264, "contentHash": "********************************", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SafeCast.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Strings"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1751998865143, "contentHash": "267d92fe4de67b1bdb3302c08f387dbf", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol": {"lastModificationDate": 1751998865169, "contentHash": "7c03c1e37c3dc24eafb76dc2b8a5c3a6", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC165"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/token/ERC721/IERC721.sol": {"lastModificationDate": 1751998865238, "contentHash": "********************************", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC721"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol": {"lastModificationDate": 1751998865240, "contentHash": "12c206f185cb951213799561fdcaa40d", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../IERC721.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC721Metadata"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol": {"lastModificationDate": 1751998865201, "contentHash": "e12814872b31b0af06106796a6621b04", "sourceName": "@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../IERC721Receiver.sol", "../../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC721Utils"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"lastModificationDate": 1751998865263, "contentHash": "ae3528afb8bdb0a7dcfba5b115ee8074", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SignedMath"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/math/SafeCast.sol": {"lastModificationDate": 1751998865261, "contentHash": "2adca1150f58fc6f3d1f0a0f22ee7cca", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeCast"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"lastModificationDate": 1751998865248, "contentHash": "5ec781e33d3a9ac91ffdc83d94420412", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../Panic.sol", "./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Math"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/Panic.sol": {"lastModificationDate": 1751998865254, "contentHash": "2133dc13536b4a6a98131e431fac59e1", "sourceName": "@openzeppelin/contracts/utils/Panic.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Panic"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"lastModificationDate": 1751998865230, "contentHash": "bf0119eb2a570f219729ff38b6cd1df8", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC165"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"lastModificationDate": 1751998865240, "contentHash": "729ce0904eb533489ffcc3bfe91237d4", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC721Receiver"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/interfaces/IERC4906.sol": {"lastModificationDate": 1751998865237, "contentHash": "273c26c9c728a6052d1175ace81d10b4", "sourceName": "@openzeppelin/contracts/interfaces/IERC4906.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["./IERC165.sol", "./IERC721.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC4906"]}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"lastModificationDate": 1751998865229, "contentHash": "f808b485ee0cdc6768ee8385ae5f9a2a", "sourceName": "@openzeppelin/contracts/interfaces/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": []}, "/Users/<USER>/Documents/sprayNft/node_modules/@openzeppelin/contracts/interfaces/IERC721.sol": {"lastModificationDate": 1751998865238, "contentHash": "d8d23fc2f55ed934d1a1b91c9afd8526", "sourceName": "@openzeppelin/contracts/interfaces/IERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "imports": ["../token/ERC721/IERC721.sol"], "versionPragmas": ["^0.8.20"], "artifacts": []}}}