{"version": "3.4", "log": [{"@openzeppelin/contracts/access/Ownable.sol:Ownable": {"src": "@openzeppelin/contracts/access/Ownable.sol:20", "inherit": ["@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["owner()", "renounceOwnership()", "transferOwnership(address)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "Ownable", "src": "@openzeppelin/contracts/access/Ownable.sol:38"}], "layout": {"storage": [{"label": "_owner", "offset": 0, "slot": "0", "type": "t_address", "contract": "Ownable", "src": "@openzeppelin/contracts/access/Ownable.sol:21"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/interfaces/IERC2981.sol:IERC2981": {"src": "@openzeppelin/contracts/interfaces/IERC2981.sol:14", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["royaltyInfo(uint256,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/interfaces/IERC4906.sol:IERC4906": {"src": "@openzeppelin/contracts/interfaces/IERC4906.sol:10", "inherit": ["@openzeppelin/contracts/token/ERC721/IERC721.sol:IERC721", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors": {"src": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:113", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors": {"src": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:9", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors": {"src": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:55", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/token/ERC721/ERC721.sol:ERC721": {"src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:19", "inherit": ["@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors", "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol:IERC721Metadata", "@openzeppelin/contracts/token/ERC721/IERC721.sol:IERC721", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": ["@openzeppelin/contracts/utils/Strings.sol:Strings", "@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol:ERC721Utils"], "methods": ["supportsInterface(bytes4)", "balanceOf(address)", "ownerOf(uint256)", "name()", "symbol()", "tokenURI(uint256)", "approve(address,uint256)", "getApproved(uint256)", "setApprovalForAll(address,bool)", "isApprovedForAll(address,address)", "transferFrom(address,address,uint256)", "safeTransferFrom(address,address,uint256)", "safeTransferFrom(address,address,uint256,bytes)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:39"}], "layout": {"storage": [{"label": "_name", "offset": 0, "slot": "0", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:23"}, {"label": "_symbol", "offset": 0, "slot": "1", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:26"}, {"label": "_owners", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:28"}, {"label": "_balances", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:30"}, {"label": "_tokenApprovals", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:32"}, {"label": "_operatorApprovals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_bool))", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:34"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_bool))": {"label": "mapping(address => mapping(address => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_address)": {"label": "mapping(uint256 => address)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/token/ERC721/IERC721.sol:IERC721": {"src": "@openzeppelin/contracts/token/ERC721/IERC721.sol:11", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["balanceOf(address)", "ownerOf(uint256)", "safeTransferFrom(address,address,uint256,bytes)", "safeTransferFrom(address,address,uint256)", "transferFrom(address,address,uint256)", "approve(address,uint256)", "setApprovalForAll(address,bool)", "getApproved(uint256)", "isApprovedForAll(address,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol:IERC721Receiver": {"src": "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol:11", "inherit": [], "libraries": [], "methods": ["onERC721Received(address,address,uint256,bytes)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol:ERC721Burnable": {"src": "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol:13", "inherit": ["@openzeppelin/contracts/token/ERC721/ERC721.sol:ERC721", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors", "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol:IERC721Metadata", "@openzeppelin/contracts/token/ERC721/IERC721.sol:IERC721", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["burn(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_name", "offset": 0, "slot": "0", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:23"}, {"label": "_symbol", "offset": 0, "slot": "1", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:26"}, {"label": "_owners", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:28"}, {"label": "_balances", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:30"}, {"label": "_tokenApprovals", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:32"}, {"label": "_operatorApprovals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_bool))", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:34"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_bool))": {"label": "mapping(address => mapping(address => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_address)": {"label": "mapping(uint256 => address)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol:ERC721URIStorage": {"src": "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol:14", "inherit": ["@openzeppelin/contracts/token/ERC721/ERC721.sol:ERC721", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors", "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol:IERC721Metadata", "@openzeppelin/contracts/interfaces/IERC4906.sol:IERC4906", "@openzeppelin/contracts/token/ERC721/IERC721.sol:IERC721", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": ["@openzeppelin/contracts/utils/Strings.sol:Strings"], "methods": ["supportsInterface(bytes4)", "tokenURI(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_name", "offset": 0, "slot": "0", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:23"}, {"label": "_symbol", "offset": 0, "slot": "1", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:26"}, {"label": "_owners", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:28"}, {"label": "_balances", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:30"}, {"label": "_tokenApprovals", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:32"}, {"label": "_operatorApprovals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_bool))", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:34"}, {"label": "_tokenURIs", "offset": 0, "slot": "6", "type": "t_mapping(t_uint256,t_string_storage)", "contract": "ERC721URIStorage", "src": "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol:22"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_bool))": {"label": "mapping(address => mapping(address => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_address)": {"label": "mapping(uint256 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_string_storage)": {"label": "mapping(uint256 => string)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol:IERC721Metadata": {"src": "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol:12", "inherit": ["@openzeppelin/contracts/token/ERC721/IERC721.sol:IERC721", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["name()", "symbol()", "tokenURI(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol:ERC721Utils": {"src": "@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol:16", "version": {"withMetadata": "576f7c59fdf9db58720fe8b583b532bbb27e7ef0356648eb02e803cf1cff3697", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/Context.sol:Context": {"src": "@openzeppelin/contracts/utils/Context.sol:16", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/Panic.sol:Panic": {"src": "@openzeppelin/contracts/utils/Panic.sol:26", "version": {"withMetadata": "475e216dbfbf13634f5a46a1e2b9e7eb3380f89234a7854491859eeaf40149d1", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/Pausable.sol:Pausable": {"src": "@openzeppelin/contracts/utils/Pausable.sol:17", "inherit": ["@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_paused", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin/contracts/utils/Pausable.sol:18"}], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol:ReentrancyGuard": {"src": "@openzeppelin/contracts/utils/ReentrancyGuard.sol:25", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "Reentrancy<PERSON><PERSON>", "src": "@openzeppelin/contracts/utils/ReentrancyGuard.sol:47"}], "layout": {"storage": [{"label": "_status", "offset": 0, "slot": "0", "type": "t_uint256", "contract": "Reentrancy<PERSON><PERSON>", "src": "@openzeppelin/contracts/utils/ReentrancyGuard.sol:40"}], "types": {"t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/Strings.sol:Strings": {"src": "@openzeppelin/contracts/utils/Strings.sol:13", "version": {"withMetadata": "950320c5e510582224831d4481f8916b86f0dadc33c2378f0c3a9a16165953e2", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/math/SafeCast.sol:SafeCast", "@openzeppelin/contracts/utils/math/Math.sol:Math", "@openzeppelin/contracts/utils/math/SignedMath.sol:SignedMath"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165": {"src": "@openzeppelin/contracts/utils/introspection/ERC165.sol:20", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165": {"src": "@openzeppelin/contracts/utils/introspection/IERC165.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/math/Math.sol:Math": {"src": "@openzeppelin/contracts/utils/math/Math.sol:12", "version": {"withMetadata": "72f841a753df86b24f0efc2c28b0a02e06fe53badfa2162f367375b7041d3b1f", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/math/SafeCast.sol:SafeCast", "@openzeppelin/contracts/utils/math/Math.sol:Math", "@openzeppelin/contracts/utils/Panic.sol:Panic"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/math/SafeCast.sol:SafeCast": {"src": "@openzeppelin/contracts/utils/math/SafeCast.sol:19", "version": {"withMetadata": "d581c33288cfa4779579536496bb900e06fbf31524ea3ebb013c8a4eff6279bd", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "@openzeppelin/contracts/utils/math/SignedMath.sol:SignedMath": {"src": "@openzeppelin/contracts/utils/math/SignedMath.sol:11", "version": {"withMetadata": "3a7a073fb49a215e66242c641d2dcc99bcd8f9b000228f6c75358cfcbefa4a94", "withoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db", "linkedWithoutMetadata": "a64c6cf9c6ba9368f5132c93a0196b3204a7963dbb4dd05dfddb4ab23126b8db"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/math/SafeCast.sol:SafeCast"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.20"}, "contracts/SprayNFT.sol:SprayNFT": {"src": "contracts/SprayNFT.sol:24", "version": {"withMetadata": "471e8cf26051fb0972896eac8c61e0e41c991286e84574704bee8272fefc4fb0", "withoutMetadata": "986b238cb5349ce2fac0bda801f9275041cabf7181d9e0d36d12941f42d68d70", "linkedWithoutMetadata": "986b238cb5349ce2fac0bda801f9275041cabf7181d9e0d36d12941f42d68d70"}, "inherit": ["@openzeppelin/contracts/interfaces/IERC2981.sol:IERC2981", "@openzeppelin/contracts/utils/Pausable.sol:Pausable", "@openzeppelin/contracts/utils/ReentrancyGuard.sol:ReentrancyGuard", "@openzeppelin/contracts/access/Ownable.sol:Ownable", "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol:ERC721Burnable", "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol:ERC721URIStorage", "@openzeppelin/contracts/token/ERC721/ERC721.sol:ERC721", "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors", "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol:IERC721Metadata", "@openzeppelin/contracts/interfaces/IERC4906.sol:IERC4906", "@openzeppelin/contracts/token/ERC721/IERC721.sol:IERC721", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(string,string,uint256,uint256,address,uint96)", "mint(address,string,address)", "batchMint(address,string[],address)", "setTokenRoyalty(uint256,address,uint96)", "setDefaultRoyalty(address,uint96)", "royaltyInfo(uint256,uint256)", "setMintingFee(uint256)", "setMaxSupply(uint256)", "pause()", "unpause()", "withdraw()", "totalSupply()", "tokensOfOwner(address)", "tokenURI(uint256)", "supportsInterface(bytes4)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:91"}], "layout": {"storage": [{"label": "_name", "offset": 0, "slot": "0", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:23"}, {"label": "_symbol", "offset": 0, "slot": "1", "type": "t_string_storage", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:26"}, {"label": "_owners", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:28"}, {"label": "_balances", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:30"}, {"label": "_tokenApprovals", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:32"}, {"label": "_operatorApprovals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_bool))", "contract": "ERC721", "src": "@openzeppelin/contracts/token/ERC721/ERC721.sol:34"}, {"label": "_tokenURIs", "offset": 0, "slot": "6", "type": "t_mapping(t_uint256,t_string_storage)", "contract": "ERC721URIStorage", "src": "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol:22"}, {"label": "_owner", "offset": 0, "slot": "7", "type": "t_address", "contract": "Ownable", "src": "@openzeppelin/contracts/access/Ownable.sol:21"}, {"label": "_status", "offset": 0, "slot": "8", "type": "t_uint256", "contract": "Reentrancy<PERSON><PERSON>", "src": "@openzeppelin/contracts/utils/ReentrancyGuard.sol:40"}, {"label": "_paused", "offset": 0, "slot": "9", "type": "t_bool", "contract": "Pausable", "src": "@openzeppelin/contracts/utils/Pausable.sol:18"}, {"label": "_tokenIdCounter", "offset": 0, "slot": "10", "type": "t_uint256", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:34"}, {"label": "_defaultRoyaltyInfo", "offset": 0, "slot": "11", "type": "t_struct(RoyaltyInfo)6946_storage", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:43"}, {"label": "_tokenRoyaltyInfo", "offset": 0, "slot": "12", "type": "t_mapping(t_uint256,t_struct(RoyaltyInfo)6946_storage)", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:46"}, {"label": "tokenCreators", "offset": 0, "slot": "13", "type": "t_mapping(t_uint256,t_address)", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:49"}, {"label": "creatorTokenCount", "offset": 0, "slot": "14", "type": "t_mapping(t_address,t_uint256)", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:52"}, {"label": "maxSupply", "offset": 0, "slot": "15", "type": "t_uint256", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:55"}, {"label": "mintingFee", "offset": 0, "slot": "16", "type": "t_uint256", "contract": "SprayNFT", "src": "contracts/SprayNFT.sol:58"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_bool))": {"label": "mapping(address => mapping(address => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_address)": {"label": "mapping(uint256 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_string_storage)": {"label": "mapping(uint256 => string)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(RoyaltyInfo)6946_storage)": {"label": "mapping(uint256 => struct SprayNFT.RoyaltyInfo)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(RoyaltyInfo)6946_storage": {"label": "struct SprayNFT.RoyaltyInfo", "members": [{"label": "recipient", "type": "t_address", "offset": 0, "slot": "0"}, {"label": "royaltyFraction", "type": "t_uint96", "offset": 20, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint96": {"label": "uint96", "numberOfBytes": "12"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.20"}}]}