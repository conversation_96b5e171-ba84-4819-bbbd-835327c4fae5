# Contributing to <PERSON><PERSON><PERSON><PERSON><PERSON> Backend

Thank you for your interest in contributing to the SprayNFT Backend! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

1. **Search existing issues** first to avoid duplicates
2. **Use the issue template** when creating new issues
3. **Provide detailed information** including:
   - Steps to reproduce
   - Expected vs actual behavior
   - Environment details (OS, Node.js version, etc.)
   - Error messages and logs

### Suggesting Features

1. **Check the roadmap** to see if the feature is already planned
2. **Open a discussion** before implementing large features
3. **Provide use cases** and explain the benefit
4. **Consider backward compatibility**

### Code Contributions

1. **Fork the repository**
2. **Create a feature branch** from `main`
3. **Make your changes** following our coding standards
4. **Write tests** for new functionality
5. **Update documentation** as needed
6. **Submit a pull request**

## 🛠️ Development Setup

### Prerequisites

- Node.js 18.x or higher
- MongoDB 6.x or higher
- npm 9.x or higher

### Quick Setup

```bash
# Clone your fork
git clone https://github.com/your-username/spray-nft-backend.git
cd spray-nft-backend

# Run quick start script
./scripts/quick-start.sh  # Linux/macOS
# or
scripts\quick-start.bat   # Windows

# Or manual setup
npm install
cp .env.example .env
# Edit .env with your configuration
npm run dev
```

### Development Workflow

1. **Create a branch** for your feature/fix:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** and test thoroughly

3. **Run tests** before committing:
   ```bash
   npm test
   npm run test:security
   npm run lint
   ```

4. **Commit your changes** with descriptive messages:
   ```bash
   git commit -m "feat: add new NFT batch minting feature"
   ```

5. **Push to your fork** and create a pull request

## 📝 Coding Standards

### Code Style

- **ESLint**: Follow the configured ESLint rules
- **Formatting**: Use consistent indentation (2 spaces)
- **Naming**: Use camelCase for variables and functions, PascalCase for classes
- **Comments**: Write clear, concise comments for complex logic

### File Structure

```
src/
├── controllers/     # Request handlers
├── middleware/      # Express middleware
├── models/         # Database models
├── routes/         # API routes
├── services/       # Business logic
├── utils/          # Utility functions
└── config/         # Configuration files
```

### API Design

- **RESTful**: Follow REST principles
- **Consistent**: Use consistent naming and response formats
- **Documented**: Document all endpoints
- **Validated**: Validate all inputs
- **Secured**: Implement proper authentication and authorization

### Database

- **Mongoose**: Use Mongoose for MongoDB interactions
- **Validation**: Implement schema validation
- **Indexing**: Add appropriate indexes for performance
- **Migrations**: Use migration scripts for schema changes

## 🧪 Testing

### Test Types

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test API endpoints and database interactions
- **Security Tests**: Test for vulnerabilities and security issues

### Writing Tests

```javascript
// Example test structure
describe('Authentication', () => {
  beforeEach(async () => {
    // Setup test data
  })

  it('should register a new user', async () => {
    const response = await request(app)
      .post('/api/auth/register')
      .send(validUserData)
      .expect(201)

    expect(response.body.success).to.be.true
  })

  afterEach(async () => {
    // Cleanup test data
  })
})
```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:security

# Run with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

## 🔒 Security Guidelines

### Security Best Practices

- **Input Validation**: Validate and sanitize all inputs
- **Authentication**: Use secure authentication methods
- **Authorization**: Implement proper access controls
- **Encryption**: Encrypt sensitive data
- **Dependencies**: Keep dependencies updated
- **Secrets**: Never commit secrets to version control

### Security Testing

- Run security tests before submitting PRs
- Use tools like `npm audit` to check for vulnerabilities
- Follow OWASP guidelines for web application security

## 📚 Documentation

### Code Documentation

- **JSDoc**: Use JSDoc comments for functions and classes
- **README**: Update README.md for significant changes
- **API Docs**: Update API documentation for new endpoints
- **Examples**: Provide usage examples

### Documentation Standards

```javascript
/**
 * Mint an NFT from artwork
 * @param {Object} user - The user object
 * @param {string} to - Recipient wallet address
 * @param {string} tokenURI - Token metadata URI
 * @param {string} creator - Creator identifier
 * @param {Object} options - Minting options
 * @returns {Promise<Object>} Transaction result
 */
async function mintNFT(user, to, tokenURI, creator, options = {}) {
  // Implementation
}
```

## 🚀 Pull Request Process

### Before Submitting

1. **Test thoroughly** on your local environment
2. **Run the full test suite** and ensure all tests pass
3. **Check code style** with ESLint
4. **Update documentation** if needed
5. **Rebase your branch** on the latest main

### PR Requirements

- **Descriptive title** and description
- **Link to related issues** if applicable
- **Test coverage** for new features
- **Documentation updates** for API changes
- **No merge conflicts** with main branch

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Security tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings or errors
```

## 🐛 Bug Reports

### Bug Report Template

```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. See error

**Expected behavior**
What you expected to happen.

**Environment:**
- OS: [e.g. Ubuntu 20.04]
- Node.js: [e.g. 18.17.0]
- npm: [e.g. 9.6.7]
- MongoDB: [e.g. 6.0.0]

**Additional context**
Any other context about the problem.
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Is your feature request related to a problem?**
A clear description of what the problem is.

**Describe the solution you'd like**
A clear description of what you want to happen.

**Describe alternatives you've considered**
Alternative solutions or features you've considered.

**Additional context**
Any other context or screenshots about the feature request.
```

## 📋 Code Review Guidelines

### For Reviewers

- **Be constructive** and helpful in feedback
- **Check for security** issues and best practices
- **Verify tests** cover the new functionality
- **Ensure documentation** is updated
- **Test the changes** locally if possible

### For Contributors

- **Respond promptly** to review feedback
- **Make requested changes** or explain why not
- **Keep discussions** focused and professional
- **Update your PR** based on feedback

## 🏷️ Versioning

We use [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

## 📄 License

By contributing to SprayNFT Backend, you agree that your contributions will be licensed under the MIT License.

## 🆘 Getting Help

- **Documentation**: Check the [docs](docs/) directory
- **Issues**: Search existing [GitHub Issues](https://github.com/your-org/spray-nft-backend/issues)
- **Discussions**: Join [GitHub Discussions](https://github.com/your-org/spray-nft-backend/discussions)
- **Discord**: Join our Discord server (link in README)

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- Special thanks in documentation

Thank you for contributing to SprayNFT Backend! 🎉
