# SprayNFT Backend Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, production, test)
NODE_ENV=development

# Server port
PORT=3001

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB connection string
MONGODB_URI=mongodb://localhost:27017/spraynft

# Test database URI
MONGODB_TEST_URI=mongodb://localhost:27017/spraynft_test

# Redis URL (optional, for caching)
REDIS_URL=redis://localhost:6379

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================

# JWT secrets (generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))")
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here

# Token expiration times
JWT_EXPIRE=15m
JWT_REFRESH_EXPIRE=7d

# =============================================================================
# ENCRYPTION
# =============================================================================

# Encryption key for private keys (32 chars, generate with: node -e "console.log(require('crypto').randomBytes(16).toString('hex'))")
ENCRYPTION_KEY=your-32-character-encryption-key-here

# =============================================================================
# BLOCKCHAIN CONFIGURATION
# =============================================================================

# Polygon RPC URLs
POLYGON_RPC_URL=https://polygon-rpc.com
MUMBAI_RPC_URL=https://rpc.ankr.com/polygon_amoy

# Deployer private key (KEEP SECURE!)
PRIVATE_KEY=your-deployer-private-key-here

# Smart contract addresses (set after deployment)
NFT_CONTRACT_ADDRESS_POLYGON=
NFT_CONTRACT_ADDRESS_MUMBAI=

# Gas settings
GAS_LIMIT=500000
GAS_PRICE_GWEI=30

# Wallet configuration
WALLET_DERIVATION_PATH=m/44'/60'/0'/0/

# =============================================================================
# IPFS/PINATA CONFIGURATION
# =============================================================================

# Pinata API credentials (get from https://pinata.cloud/)
PINATA_API_KEY=your-pinata-api-key
PINATA_SECRET_KEY=your-pinata-secret-key

# IPFS gateway URL (optional)
IPFS_GATEWAY_URL=https://gateway.pinata.cloud/ipfs/

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Allowed CORS origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# API key settings
REQUIRE_API_KEY=false
API_KEY=your-api-key-here

# IP whitelist (comma-separated, leave empty for all)
IP_WHITELIST=

# Security settings
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate limiting settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================

# File upload configuration
MAX_FILE_SIZE=104857600
UPLOAD_DIR=uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,model/gltf-binary,model/gltf+json,application/zip

# =============================================================================
# LOGGING & MONITORING
# =============================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Log file paths
LOG_FILE=logs/app.log
ERROR_LOG_FILE=logs/error.log

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# =============================================================================
# DOCKER SETTINGS (for Docker deployment)
# =============================================================================

# MongoDB Docker settings
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=secure-password-here
MONGO_DATABASE=spraynft

# Grafana password
GRAFANA_PASSWORD=admin
