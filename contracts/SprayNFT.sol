// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/interfaces/IERC2981.sol";

/**
 * @title SprayNFT
 * @dev ERC721 contract for minting Unity AR artwork as NFTs
 * Features:
 * - ERC721 compliant NFT minting
 * - URI storage for IPFS metadata
 * - Royalty support (EIP-2981)
 * - Pausable functionality
 * - Burnable tokens
 * - Access control
 * - Reentrancy protection
 */
contract SprayNFT is 
    ERC721, 
    ERC721URIStorage, 
    ERC721Burnable, 
    Ownable, 
    ReentrancyGuard, 
    Pausable,
    IERC2981 
{
    // Token ID counter
    uint256 private _tokenIdCounter;

    // Royalty information
    struct RoyaltyInfo {
        address recipient;
        uint96 royaltyFraction; // Basis points (e.g., 250 = 2.5%)
    }

    // Default royalty info
    RoyaltyInfo private _defaultRoyaltyInfo;

    // Token-specific royalty info
    mapping(uint256 => RoyaltyInfo) private _tokenRoyaltyInfo;

    // Mapping from token ID to creator
    mapping(uint256 => address) public tokenCreators;

    // Mapping from creator to their token count
    mapping(address => uint256) public creatorTokenCount;

    // Maximum supply (0 = unlimited)
    uint256 public maxSupply;

    // Minting fee in wei
    uint256 public mintingFee;

    // Events
    event TokenMinted(
        uint256 indexed tokenId,
        address indexed creator,
        address indexed to,
        string tokenURI
    );

    event RoyaltySet(
        uint256 indexed tokenId,
        address indexed recipient,
        uint96 royaltyFraction
    );

    event DefaultRoyaltySet(
        address indexed recipient,
        uint96 royaltyFraction
    );

    event MintingFeeUpdated(uint256 newFee);
    event MaxSupplyUpdated(uint256 newMaxSupply);

    /**
     * @dev Constructor
     * @param _name Token name
     * @param _symbol Token symbol
     * @param _maxSupply Maximum token supply (0 for unlimited)
     * @param _mintingFee Fee required to mint (in wei)
     * @param _royaltyRecipient Default royalty recipient
     * @param _royaltyFraction Default royalty fraction in basis points
     */
    constructor(
        string memory _name,
        string memory _symbol,
        uint256 _maxSupply,
        uint256 _mintingFee,
        address _royaltyRecipient,
        uint96 _royaltyFraction
    ) ERC721(_name, _symbol) Ownable(msg.sender) {
        maxSupply = _maxSupply;
        mintingFee = _mintingFee;
        
        if (_royaltyRecipient != address(0)) {
            _setDefaultRoyalty(_royaltyRecipient, _royaltyFraction);
        }
    }

    /**
     * @dev Mint a new NFT
     * @param to Address to mint the token to
     * @param uri IPFS URI for token metadata
     * @param creator Original creator of the artwork
     */
    function mint(
        address to,
        string memory uri,
        address creator
    ) public payable nonReentrant whenNotPaused returns (uint256) {
        require(to != address(0), "SprayNFT: mint to zero address");
        require(bytes(uri).length > 0, "SprayNFT: empty token URI");
        require(creator != address(0), "SprayNFT: creator is zero address");
        require(msg.value >= mintingFee, "SprayNFT: insufficient minting fee");

        // Check max supply
        if (maxSupply > 0) {
            require(_tokenIdCounter < maxSupply, "SprayNFT: max supply reached");
        }

        uint256 tokenId = _tokenIdCounter;
        _tokenIdCounter++;

        // Mint the token
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, uri);

        // Set creator information
        tokenCreators[tokenId] = creator;
        creatorTokenCount[creator]++;

        emit TokenMinted(tokenId, creator, to, uri);

        return tokenId;
    }

    /**
     * @dev Batch mint multiple NFTs
     * @param to Address to mint tokens to
     * @param uris Array of IPFS URIs for token metadata
     * @param creator Original creator of the artworks
     */
    function batchMint(
        address to,
        string[] memory uris,
        address creator
    ) public payable nonReentrant whenNotPaused returns (uint256[] memory) {
        require(to != address(0), "SprayNFT: mint to zero address");
        require(uris.length > 0, "SprayNFT: empty token URIs array");
        require(creator != address(0), "SprayNFT: creator is zero address");
        require(msg.value >= mintingFee * uris.length, "SprayNFT: insufficient minting fee");

        uint256[] memory tokenIds = new uint256[](uris.length);

        for (uint256 i = 0; i < uris.length; i++) {
            require(bytes(uris[i]).length > 0, "SprayNFT: empty token URI");
            
            // Check max supply
            if (maxSupply > 0) {
                require(_tokenIdCounter < maxSupply, "SprayNFT: max supply reached");
            }

            uint256 tokenId = _tokenIdCounter;
            _tokenIdCounter++;

            _safeMint(to, tokenId);
            _setTokenURI(tokenId, uris[i]);

            tokenCreators[tokenId] = creator;
            tokenIds[i] = tokenId;

            emit TokenMinted(tokenId, creator, to, uris[i]);
        }

        creatorTokenCount[creator] += uris.length;
        return tokenIds;
    }

    /**
     * @dev Set royalty for a specific token
     * @param tokenId Token ID
     * @param recipient Royalty recipient
     * @param royaltyFraction Royalty fraction in basis points
     */
    function setTokenRoyalty(
        uint256 tokenId,
        address recipient,
        uint96 royaltyFraction
    ) public {
        require(_ownerOf(tokenId) != address(0), "SprayNFT: token does not exist");
        require(
            msg.sender == owner() || msg.sender == tokenCreators[tokenId],
            "SprayNFT: not authorized to set royalty"
        );
        require(recipient != address(0), "SprayNFT: royalty recipient is zero address");
        require(royaltyFraction <= 1000, "SprayNFT: royalty fraction too high"); // Max 10%

        _tokenRoyaltyInfo[tokenId] = RoyaltyInfo(recipient, royaltyFraction);
        emit RoyaltySet(tokenId, recipient, royaltyFraction);
    }

    /**
     * @dev Set default royalty
     * @param recipient Royalty recipient
     * @param royaltyFraction Royalty fraction in basis points
     */
    function setDefaultRoyalty(
        address recipient,
        uint96 royaltyFraction
    ) public onlyOwner {
        _setDefaultRoyalty(recipient, royaltyFraction);
    }

    /**
     * @dev Internal function to set default royalty
     */
    function _setDefaultRoyalty(
        address recipient,
        uint96 royaltyFraction
    ) internal {
        require(recipient != address(0), "SprayNFT: royalty recipient is zero address");
        require(royaltyFraction <= 1000, "SprayNFT: royalty fraction too high"); // Max 10%

        _defaultRoyaltyInfo = RoyaltyInfo(recipient, royaltyFraction);
        emit DefaultRoyaltySet(recipient, royaltyFraction);
    }

    /**
     * @dev Get royalty information for a token
     * @param tokenId Token ID
     * @param salePrice Sale price of the token
     */
    function royaltyInfo(
        uint256 tokenId,
        uint256 salePrice
    ) public view override returns (address, uint256) {
        RoyaltyInfo memory royalty = _tokenRoyaltyInfo[tokenId];

        if (royalty.recipient == address(0)) {
            royalty = _defaultRoyaltyInfo;
        }

        uint256 royaltyAmount = (salePrice * royalty.royaltyFraction) / 10000;
        return (royalty.recipient, royaltyAmount);
    }

    /**
     * @dev Update minting fee (only owner)
     */
    function setMintingFee(uint256 _mintingFee) public onlyOwner {
        mintingFee = _mintingFee;
        emit MintingFeeUpdated(_mintingFee);
    }

    /**
     * @dev Update max supply (only owner)
     */
    function setMaxSupply(uint256 _maxSupply) public onlyOwner {
        require(_maxSupply == 0 || _maxSupply >= _tokenIdCounter, "SprayNFT: max supply too low");
        maxSupply = _maxSupply;
        emit MaxSupplyUpdated(_maxSupply);
    }

    /**
     * @dev Pause contract (only owner)
     */
    function pause() public onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause contract (only owner)
     */
    function unpause() public onlyOwner {
        _unpause();
    }

    /**
     * @dev Withdraw contract balance (only owner)
     */
    function withdraw() public onlyOwner nonReentrant {
        uint256 balance = address(this).balance;
        require(balance > 0, "SprayNFT: no funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "SprayNFT: withdrawal failed");
    }

    /**
     * @dev Get total supply
     */
    function totalSupply() public view returns (uint256) {
        return _tokenIdCounter;
    }

    /**
     * @dev Get tokens owned by an address
     */
    function tokensOfOwner(address owner) public view returns (uint256[] memory) {
        uint256 tokenCount = balanceOf(owner);
        uint256[] memory tokenIds = new uint256[](tokenCount);
        uint256 currentIndex = 0;

        for (uint256 i = 0; i < _tokenIdCounter; i++) {
            if (_ownerOf(i) != address(0) && ownerOf(i) == owner) {
                tokenIds[currentIndex] = i;
                currentIndex++;
            }
        }

        return tokenIds;
    }

    // Required overrides
    function tokenURI(uint256 tokenId) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId) public view override(ERC721, ERC721URIStorage, IERC165) returns (bool) {
        return interfaceId == type(IERC2981).interfaceId || super.supportsInterface(interfaceId);
    }
}
