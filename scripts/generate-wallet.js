const { ethers } = require('ethers')

// Generate a random wallet for testing
const wallet = ethers.Wallet.createRandom()

console.log('Generated Test Wallet:')
console.log('Address:', wallet.address)
console.log('Private Key:', wallet.privateKey)
console.log('')
console.log('⚠️  WARNING: This is for testing only!')
console.log('⚠️  Do NOT use this wallet on mainnet!')
console.log('⚠️  Do NOT send real funds to this address!')
console.log('')
console.log('To use this wallet:')
console.log('1. Add the private key to your .env file')
console.log('2. Get test MATIC from Polygon Amoy faucet:')
console.log('   https://faucet.polygon.technology/')
console.log('3. Deploy the contract with: npm run deploy:testnet')
