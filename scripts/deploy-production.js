const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

/**
 * Production deployment script
 * This script handles the complete deployment process for production
 */

const config = {
  // Environment settings
  NODE_ENV: 'production',
  
  // Required environment variables
  requiredEnvVars: [
    'MONGODB_URI',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'ENCRYPTION_KEY',
    'PINATA_API_KEY',
    'PINATA_SECRET_KEY',
    'POLYGON_RPC_URL',
    'PRIVATE_KEY',
    'NFT_CONTRACT_ADDRESS_POLYGON'
  ],
  
  // Optional environment variables with defaults
  optionalEnvVars: {
    PORT: '3001',
    ALLOWED_ORIGINS: 'https://yourdomain.com',
    REQUIRE_API_KEY: 'false',
    IP_WHITELIST: '',
    LOG_LEVEL: 'info'
  }
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function error(message) {
  log(`❌ ERROR: ${message}`, 'red')
}

function success(message) {
  log(`✅ ${message}`, 'green')
}

function warning(message) {
  log(`⚠️  WARNING: ${message}`, 'yellow')
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue')
}

// Check if running in production environment
function checkEnvironment() {
  info('Checking deployment environment...')
  
  if (process.env.NODE_ENV !== 'production') {
    warning('NODE_ENV is not set to production')
    process.env.NODE_ENV = 'production'
  }
  
  success('Environment check passed')
}

// Validate required environment variables
function validateEnvironment() {
  info('Validating environment variables...')
  
  const missing = []
  
  // Check required variables
  config.requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName)
    }
  })
  
  if (missing.length > 0) {
    error(`Missing required environment variables: ${missing.join(', ')}`)
    error('Please set these variables before deploying to production')
    process.exit(1)
  }
  
  // Set optional variables with defaults
  Object.entries(config.optionalEnvVars).forEach(([varName, defaultValue]) => {
    if (!process.env[varName]) {
      process.env[varName] = defaultValue
      info(`Set ${varName} to default value: ${defaultValue}`)
    }
  })
  
  success('Environment validation passed')
}

// Run security audit
function runSecurityAudit() {
  info('Running security audit...')
  
  try {
    execSync('npm audit --audit-level=high', { stdio: 'inherit' })
    success('Security audit passed')
  } catch (error) {
    error('Security audit failed')
    warning('Please fix security vulnerabilities before deploying')
    process.exit(1)
  }
}

// Run tests
function runTests() {
  info('Running test suite...')
  
  try {
    execSync('npm test', { stdio: 'inherit' })
    success('All tests passed')
  } catch (error) {
    error('Tests failed')
    process.exit(1)
  }
}

// Install production dependencies
function installDependencies() {
  info('Installing production dependencies...')
  
  try {
    execSync('npm ci --only=production', { stdio: 'inherit' })
    success('Dependencies installed')
  } catch (error) {
    error('Failed to install dependencies')
    process.exit(1)
  }
}

// Compile smart contracts
function compileContracts() {
  info('Compiling smart contracts...')
  
  try {
    execSync('npm run compile', { stdio: 'inherit' })
    success('Smart contracts compiled')
  } catch (error) {
    error('Failed to compile smart contracts')
    process.exit(1)
  }
}

// Deploy smart contracts (if needed)
function deployContracts() {
  info('Checking smart contract deployment...')
  
  if (!process.env.NFT_CONTRACT_ADDRESS_POLYGON) {
    info('Deploying smart contracts to Polygon mainnet...')
    
    try {
      execSync('npm run deploy:mainnet', { stdio: 'inherit' })
      success('Smart contracts deployed')
    } catch (error) {
      error('Failed to deploy smart contracts')
      process.exit(1)
    }
  } else {
    info('Smart contracts already deployed')
  }
}

// Create production build
function createBuild() {
  info('Creating production build...')
  
  // Create uploads directory
  const uploadsDir = path.join(__dirname, '../uploads')
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true })
  }
  
  // Create logs directory
  const logsDir = path.join(__dirname, '../logs')
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true })
  }
  
  success('Production build created')
}

// Validate database connection
async function validateDatabase() {
  info('Validating database connection...')
  
  try {
    const mongoose = require('mongoose')
    await mongoose.connect(process.env.MONGODB_URI)
    await mongoose.connection.close()
    success('Database connection validated')
  } catch (error) {
    error(`Database connection failed: ${error.message}`)
    process.exit(1)
  }
}

// Validate blockchain connection
async function validateBlockchain() {
  info('Validating blockchain connection...')
  
  try {
    const { ethers } = require('ethers')
    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_RPC_URL)
    await provider.getNetwork()
    success('Blockchain connection validated')
  } catch (error) {
    error(`Blockchain connection failed: ${error.message}`)
    process.exit(1)
  }
}

// Validate IPFS connection
async function validateIPFS() {
  info('Validating IPFS connection...')
  
  try {
    const axios = require('axios')
    const response = await axios.get('https://api.pinata.cloud/data/testAuthentication', {
      headers: {
        'pinata_api_key': process.env.PINATA_API_KEY,
        'pinata_secret_api_key': process.env.PINATA_SECRET_KEY
      }
    })
    
    if (response.data.message === 'Congratulations! You are communicating with the Pinata API!') {
      success('IPFS connection validated')
    } else {
      throw new Error('Invalid Pinata response')
    }
  } catch (error) {
    error(`IPFS connection failed: ${error.message}`)
    process.exit(1)
  }
}

// Generate deployment report
function generateReport() {
  info('Generating deployment report...')
  
  const report = {
    timestamp: new Date().toISOString(),
    environment: 'production',
    nodeVersion: process.version,
    npmVersion: execSync('npm --version', { encoding: 'utf8' }).trim(),
    dependencies: JSON.parse(fs.readFileSync('package.json', 'utf8')).dependencies,
    configuration: {
      port: process.env.PORT,
      nodeEnv: process.env.NODE_ENV,
      mongoUri: process.env.MONGODB_URI ? '[CONFIGURED]' : '[MISSING]',
      contractAddress: process.env.NFT_CONTRACT_ADDRESS_POLYGON || '[NOT SET]'
    }
  }
  
  fs.writeFileSync('deployment-report.json', JSON.stringify(report, null, 2))
  success('Deployment report generated')
}

// Main deployment function
async function deploy() {
  try {
    log('\n🚀 Starting production deployment...\n', 'cyan')
    
    // Pre-deployment checks
    checkEnvironment()
    validateEnvironment()
    runSecurityAudit()
    runTests()
    
    // Build and compile
    installDependencies()
    compileContracts()
    createBuild()
    
    // Deploy contracts if needed
    deployContracts()
    
    // Validate connections
    await validateDatabase()
    await validateBlockchain()
    await validateIPFS()
    
    // Generate report
    generateReport()
    
    log('\n🎉 Production deployment completed successfully!\n', 'green')
    
    // Display next steps
    log('Next steps:', 'cyan')
    log('1. Start the application: npm start')
    log('2. Monitor logs: tail -f logs/app.log')
    log('3. Check health: curl http://localhost:3001/health')
    log('4. Monitor metrics: curl http://localhost:3001/metrics')
    
  } catch (error) {
    error(`Deployment failed: ${error.message}`)
    process.exit(1)
  }
}

// Handle command line arguments
const args = process.argv.slice(2)

if (args.includes('--help') || args.includes('-h')) {
  log('Production Deployment Script', 'cyan')
  log('Usage: node scripts/deploy-production.js [options]')
  log('')
  log('Options:')
  log('  --help, -h     Show this help message')
  log('  --skip-tests   Skip running tests (not recommended)')
  log('  --skip-audit   Skip security audit (not recommended)')
  log('')
  log('Environment Variables Required:')
  config.requiredEnvVars.forEach(varName => {
    log(`  ${varName}`)
  })
  process.exit(0)
}

// Skip tests if requested (not recommended)
if (args.includes('--skip-tests')) {
  warning('Skipping tests as requested')
  runTests = () => info('Tests skipped')
}

// Skip audit if requested (not recommended)
if (args.includes('--skip-audit')) {
  warning('Skipping security audit as requested')
  runSecurityAudit = () => info('Security audit skipped')
}

// Run deployment
if (require.main === module) {
  deploy()
}

module.exports = { deploy }
