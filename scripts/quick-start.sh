#!/bin/bash

# SprayNFT Backend Quick Start Script
# This script helps you get started with the SprayNFT backend quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to generate secure random string
generate_secret() {
    node -e "console.log(require('crypto').randomBytes($1).toString('hex'))"
}

print_status "🚀 SprayNFT Backend Quick Start"
echo "=================================="
echo

# Check prerequisites
print_status "Checking prerequisites..."

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_success "Node.js found: $NODE_VERSION"
else
    print_error "Node.js is not installed. Please install Node.js 18.x or higher."
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_success "npm found: $NPM_VERSION"
else
    print_error "npm is not installed. Please install npm."
    exit 1
fi

# Check MongoDB
if command_exists mongod; then
    print_success "MongoDB found"
else
    print_warning "MongoDB not found. You'll need to install MongoDB or use Docker."
fi

# Check Docker (optional)
if command_exists docker; then
    print_success "Docker found (optional)"
else
    print_warning "Docker not found (optional for development)"
fi

echo

# Install dependencies
print_status "Installing dependencies..."
npm install
print_success "Dependencies installed"

echo

# Setup environment file
print_status "Setting up environment configuration..."

if [ ! -f .env ]; then
    cp .env.example .env
    print_success "Created .env file from template"
    
    # Generate secure secrets
    print_status "Generating secure secrets..."
    
    JWT_SECRET=$(generate_secret 64)
    JWT_REFRESH_SECRET=$(generate_secret 64)
    ENCRYPTION_KEY=$(generate_secret 16)
    
    # Update .env file with generated secrets
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" .env
        sed -i '' "s/JWT_REFRESH_SECRET=.*/JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET/" .env
        sed -i '' "s/ENCRYPTION_KEY=.*/ENCRYPTION_KEY=$ENCRYPTION_KEY/" .env
    else
        # Linux
        sed -i "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" .env
        sed -i "s/JWT_REFRESH_SECRET=.*/JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET/" .env
        sed -i "s/ENCRYPTION_KEY=.*/ENCRYPTION_KEY=$ENCRYPTION_KEY/" .env
    fi
    
    print_success "Generated secure secrets"
else
    print_warning ".env file already exists, skipping creation"
fi

echo

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p uploads logs
print_success "Created uploads and logs directories"

echo

# Compile smart contracts
print_status "Compiling smart contracts..."
npm run compile
print_success "Smart contracts compiled"

echo

# Setup database
print_status "Setting up database..."

# Check if MongoDB is running
if command_exists mongod && pgrep -x "mongod" > /dev/null; then
    print_success "MongoDB is running"
elif command_exists docker; then
    print_status "Starting MongoDB with Docker..."
    docker run -d --name spraynft-mongo -p 27017:27017 mongo:6.0
    print_success "MongoDB started with Docker"
else
    print_warning "Please start MongoDB manually or use Docker"
fi

echo

# Ask user for configuration
print_status "Configuration setup..."
echo "Please configure the following in your .env file:"
echo "1. PINATA_API_KEY and PINATA_SECRET_KEY (get from https://pinata.cloud/)"
echo "2. POLYGON_RPC_URL (or use default)"
echo "3. PRIVATE_KEY (for smart contract deployment)"
echo

read -p "Do you want to open the .env file for editing now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command_exists code; then
        code .env
    elif command_exists nano; then
        nano .env
    elif command_exists vim; then
        vim .env
    else
        print_warning "No editor found. Please edit .env manually."
    fi
fi

echo

# Start the application
print_status "Starting the application..."
echo "You can start the application with:"
echo "  npm run dev    (development mode)"
echo "  npm start      (production mode)"
echo

read -p "Do you want to start the development server now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting development server..."
    npm run dev
else
    print_success "Setup complete!"
    echo
    echo "Next steps:"
    echo "1. Configure your .env file with API keys"
    echo "2. Start the server: npm run dev"
    echo "3. Visit http://localhost:3001/health to check status"
    echo "4. Check the README.md for API documentation"
    echo
    echo "For Unity integration, see: unity-integration/README.md"
    echo
fi
