@echo off
setlocal enabledelayedexpansion

REM SprayNFT Backend Quick Start Script for Windows
REM This script helps you get started with the SprayNFT backend quickly

echo.
echo ================================
echo 🚀 SprayNFT Backend Quick Start
echo ================================
echo.

REM Check prerequisites
echo [INFO] Checking prerequisites...

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18.x or higher.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js found: !NODE_VERSION!
)

REM Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed. Please install npm.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo [SUCCESS] npm found: !NPM_VERSION!
)

REM Check MongoDB
mongod --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] MongoDB not found. You'll need to install MongoDB or use Docker.
) else (
    echo [SUCCESS] MongoDB found
)

REM Check Docker (optional)
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Docker not found (optional for development)
) else (
    echo [SUCCESS] Docker found (optional)
)

echo.

REM Install dependencies
echo [INFO] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed

echo.

REM Setup environment file
echo [INFO] Setting up environment configuration...

if not exist .env (
    copy .env.example .env >nul
    echo [SUCCESS] Created .env file from template
    
    echo [INFO] Generating secure secrets...
    
    REM Generate secrets using Node.js
    for /f "tokens=*" %%i in ('node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"') do set JWT_SECRET=%%i
    for /f "tokens=*" %%i in ('node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"') do set JWT_REFRESH_SECRET=%%i
    for /f "tokens=*" %%i in ('node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"') do set ENCRYPTION_KEY=%%i
    
    REM Update .env file with generated secrets
    powershell -Command "(gc .env) -replace 'JWT_SECRET=.*', 'JWT_SECRET=!JWT_SECRET!' | Out-File -encoding ASCII .env"
    powershell -Command "(gc .env) -replace 'JWT_REFRESH_SECRET=.*', 'JWT_REFRESH_SECRET=!JWT_REFRESH_SECRET!' | Out-File -encoding ASCII .env"
    powershell -Command "(gc .env) -replace 'ENCRYPTION_KEY=.*', 'ENCRYPTION_KEY=!ENCRYPTION_KEY!' | Out-File -encoding ASCII .env"
    
    echo [SUCCESS] Generated secure secrets
) else (
    echo [WARNING] .env file already exists, skipping creation
)

echo.

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist uploads mkdir uploads
if not exist logs mkdir logs
echo [SUCCESS] Created uploads and logs directories

echo.

REM Compile smart contracts
echo [INFO] Compiling smart contracts...
call npm run compile
if %errorlevel% neq 0 (
    echo [ERROR] Failed to compile smart contracts
    pause
    exit /b 1
)
echo [SUCCESS] Smart contracts compiled

echo.

REM Setup database
echo [INFO] Setting up database...

REM Check if MongoDB is running
tasklist /FI "IMAGENAME eq mongod.exe" 2>NUL | find /I /N "mongod.exe">NUL
if %errorlevel% equ 0 (
    echo [SUCCESS] MongoDB is running
) else (
    docker --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo [INFO] Starting MongoDB with Docker...
        docker run -d --name spraynft-mongo -p 27017:27017 mongo:6.0
        echo [SUCCESS] MongoDB started with Docker
    ) else (
        echo [WARNING] Please start MongoDB manually or use Docker
    )
)

echo.

REM Configuration setup
echo [INFO] Configuration setup...
echo Please configure the following in your .env file:
echo 1. PINATA_API_KEY and PINATA_SECRET_KEY (get from https://pinata.cloud/)
echo 2. POLYGON_RPC_URL (or use default)
echo 3. PRIVATE_KEY (for smart contract deployment)
echo.

set /p EDIT_ENV="Do you want to open the .env file for editing now? (y/n): "
if /i "%EDIT_ENV%"=="y" (
    if exist "%ProgramFiles%\Microsoft VS Code\Code.exe" (
        "%ProgramFiles%\Microsoft VS Code\Code.exe" .env
    ) else if exist "%ProgramFiles(x86)%\Microsoft VS Code\Code.exe" (
        "%ProgramFiles(x86)%\Microsoft VS Code\Code.exe" .env
    ) else (
        notepad .env
    )
)

echo.

REM Start the application
echo [INFO] Starting the application...
echo You can start the application with:
echo   npm run dev    (development mode)
echo   npm start      (production mode)
echo.

set /p START_DEV="Do you want to start the development server now? (y/n): "
if /i "%START_DEV%"=="y" (
    echo [INFO] Starting development server...
    call npm run dev
) else (
    echo [SUCCESS] Setup complete!
    echo.
    echo Next steps:
    echo 1. Configure your .env file with API keys
    echo 2. Start the server: npm run dev
    echo 3. Visit http://localhost:3001/health to check status
    echo 4. Check the README.md for API documentation
    echo.
    echo For Unity integration, see: unity-integration/README.md
    echo.
    pause
)

endlocal
