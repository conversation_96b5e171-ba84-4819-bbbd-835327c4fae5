const { ethers, upgrades } = require('hardhat')
const fs = require('fs')
const path = require('path')

async function main() {
  console.log('Starting SprayNFT deployment...')

  // Get the deployer account
  const [deployer] = await ethers.getSigners()
  console.log('Deploying contracts with account:', deployer.address)

  // Check deployer balance
  const balance = await deployer.getBalance()
  console.log('Account balance:', ethers.utils.formatEther(balance), 'ETH')

  // Contract parameters
  const contractParams = {
    name: 'SprayNFT',
    symbol: 'SPRAY',
    maxSupply: 0, // Unlimited supply
    mintingFee: ethers.utils.parseEther('0.001'), // 0.001 MATIC
    royaltyRecipient: deployer.address, // Default royalty recipient
    royaltyFraction: 250 // 2.5% royalty
  }

  console.log('Contract parameters:')
  console.log('- Name:', contractParams.name)
  console.log('- Symbol:', contractParams.symbol)
  console.log('- Max Supply:', contractParams.maxSupply === 0 ? 'Unlimited' : contractParams.maxSupply)
  console.log('- Minting Fee:', ethers.utils.formatEther(contractParams.mintingFee), 'MATIC')
  console.log('- Royalty Recipient:', contractParams.royaltyRecipient)
  console.log('- Royalty Fraction:', contractParams.royaltyFraction / 100, '%')

  // Get the contract factory
  const SprayNFT = await ethers.getContractFactory('SprayNFT')

  // Deploy the contract
  console.log('\nDeploying SprayNFT contract...')
  const sprayNFT = await SprayNFT.deploy(
    contractParams.name,
    contractParams.symbol,
    contractParams.maxSupply,
    contractParams.mintingFee,
    contractParams.royaltyRecipient,
    contractParams.royaltyFraction
  )

  // Wait for deployment
  await sprayNFT.deployed()

  console.log('SprayNFT deployed to:', sprayNFT.address)
  console.log('Transaction hash:', sprayNFT.deployTransaction.hash)

  // Wait for a few confirmations
  console.log('Waiting for confirmations...')
  await sprayNFT.deployTransaction.wait(5)

  // Verify contract deployment
  console.log('\nVerifying contract deployment...')
  const deployedCode = await ethers.provider.getCode(sprayNFT.address)
  if (deployedCode === '0x') {
    throw new Error('Contract deployment failed - no code at address')
  }

  // Test basic contract functions
  console.log('\nTesting basic contract functions...')
  const name = await sprayNFT.name()
  const symbol = await sprayNFT.symbol()
  const owner = await sprayNFT.owner()
  const totalSupply = await sprayNFT.totalSupply()
  const mintingFee = await sprayNFT.mintingFee()

  console.log('- Name:', name)
  console.log('- Symbol:', symbol)
  console.log('- Owner:', owner)
  console.log('- Total Supply:', totalSupply.toString())
  console.log('- Minting Fee:', ethers.utils.formatEther(mintingFee), 'MATIC')

  // Save deployment information
  const deploymentInfo = {
    network: hre.network.name,
    contractAddress: sprayNFT.address,
    deployerAddress: deployer.address,
    transactionHash: sprayNFT.deployTransaction.hash,
    blockNumber: sprayNFT.deployTransaction.blockNumber,
    gasUsed: sprayNFT.deployTransaction.gasLimit?.toString(),
    gasPrice: sprayNFT.deployTransaction.gasPrice?.toString(),
    timestamp: new Date().toISOString(),
    contractParams,
    abi: JSON.parse(sprayNFT.interface.format('json'))
  }

  // Create deployments directory if it doesn't exist
  const deploymentsDir = path.join(__dirname, '..', 'deployments')
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true })
  }

  // Save deployment info to file
  const deploymentFile = path.join(deploymentsDir, `${hre.network.name}.json`)
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2))

  console.log(`\nDeployment information saved to: ${deploymentFile}`)

  // Update environment variables template
  console.log('\nUpdating environment variables...')
  const envKey = hre.network.name === 'polygon' ? 'NFT_CONTRACT_ADDRESS_POLYGON' : 'NFT_CONTRACT_ADDRESS_MUMBAI'
  console.log(`Please update your .env file with:`)
  console.log(`${envKey}=${sprayNFT.address}`)

  // Verification instructions
  if (hre.network.name !== 'hardhat' && hre.network.name !== 'localhost') {
    console.log('\nTo verify the contract on PolygonScan, run:')
    console.log(`npx hardhat verify --network ${hre.network.name} ${sprayNFT.address} "${contractParams.name}" "${contractParams.symbol}" ${contractParams.maxSupply} ${contractParams.mintingFee} "${contractParams.royaltyRecipient}" ${contractParams.royaltyFraction}`)
  }

  console.log('\n✅ Deployment completed successfully!')
  
  return {
    contract: sprayNFT,
    address: sprayNFT.address,
    deploymentInfo
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Deployment failed:', error)
    process.exit(1)
  })
