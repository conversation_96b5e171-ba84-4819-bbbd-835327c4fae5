{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:15:16"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:18:31"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:18:31"}
{"_message":"User validation failed","errors":{"wallet.address":{"kind":"required","message":"Path `wallet.address` is required.","name":"ValidatorError","path":"wallet.address","properties":{"message":"Path `wallet.address` is required.","path":"wallet.address","type":"required"}}},"level":"error","message":"Registration error: User validation failed: wallet.address: Path `wallet.address` is required.","service":"spray-nft-backend","stack":"ValidationError: User validation failed: wallet.address: Path `wallet.address` is required.\n    at Document.invalidate (/Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/document.js:3352:32)\n    at /Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/document.js:3113:17\n    at /Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-07-08 18:20:57"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:23"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:23"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:36"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:36"}
{"level":"info","message":"New user registered: <EMAIL>","service":"spray-nft-backend","timestamp":"2025-07-08 18:40:56"}
{"level":"info","message":"User logged in: <EMAIL>","service":"spray-nft-backend","timestamp":"2025-07-08 18:41:54"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"rpc-mumbai.maticvigil.com","level":"error","message":"Get wallet info error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com","service":"spray-nft-backend","stack":"Error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:118:26)","syscall":"getaddrinfo","timestamp":"2025-07-08 18:42:24"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"rpc-mumbai.maticvigil.com","level":"error","message":"Get wallet info error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com","service":"spray-nft-backend","stack":"Error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:118:26)","syscall":"getaddrinfo","timestamp":"2025-07-08 18:43:19"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:43:51"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:43:51"}
{"code":"SERVER_ERROR","info":{"requestUrl":"https://rpc.ankr.com/polygon_mumbai","responseBody":"{\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","responseStatus":"403 Forbidden"},"level":"error","message":"Get wallet info error: server response 403 Forbidden (request={  }, response={  }, error=null, info={ \"requestUrl\": \"https://rpc.ankr.com/polygon_mumbai\", \"responseBody\": \"{\\\"error\\\":\\\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\\\"}\", \"responseStatus\": \"403 Forbidden\" }, code=SERVER_ERROR, version=6.15.0)","request":{},"response":{},"service":"spray-nft-backend","shortMessage":"server response 403 Forbidden","stack":"Error: server response 403 Forbidden (request={  }, response={  }, error=null, info={ \"requestUrl\": \"https://rpc.ankr.com/polygon_mumbai\", \"responseBody\": \"{\\\"error\\\":\\\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\\\"}\", \"responseStatus\": \"403 Forbidden\" }, code=SERVER_ERROR, version=6.15.0)\n    at makeError (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/errors.js:137:21)\n    at assert (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/errors.js:157:15)\n    at FetchResponse.assertOk (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/fetch.js:839:32)\n    at JsonRpcProvider._send (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:917:18)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:281:40","timestamp":"2025-07-08 18:44:30"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:45:07"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:45:07"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:51:37"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:51:37"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:52:58"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:52:58"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:53:54"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:53:54"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:55:07"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:55:07"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:55:37"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:55:37"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:56:02"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:56:02"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:56:27"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:56:27"}
{"error":{"details":"Invalid API key provided: not found","reason":"INVALID_API_KEYS"},"level":"error","message":"Pinata connection test failed:","service":"spray-nft-backend","timestamp":"2025-07-08 18:57:45"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:04:03"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:04:03"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:04:50"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:04:50"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:05:15"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:05:15"}
{"level":"info","message":"Blockchain service initialized for amoy network","service":"spray-nft-backend","timestamp":"2025-07-08 19:05:39"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:05:39"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:05:39"}
{"level":"info","message":"Blockchain service initialized for amoy network","service":"spray-nft-backend","timestamp":"2025-07-08 19:06:22"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:06:22"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:06:22"}
{"level":"error","message":"Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:83:15)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getContractInfo (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:375:35)\n    at getContractInfo (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:362:50)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:13:21"}
{"level":"error","message":"Failed to get contract info: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:106:13)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getContractInfo (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:375:35)\n    at getContractInfo (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:362:50)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:13:21"}
{"level":"error","message":"Get contract info error: Failed to get contract info: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to get contract info: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.getContractInfo (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:401:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getContractInfo (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:362:26)","timestamp":"2025-07-08 19:13:21"}
{"level":"error","message":"Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:83:15)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getUserNFTs (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:410:35)\n    at getUserNFTs (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:330:42)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:16:41"}
{"level":"error","message":"Failed to get user NFTs: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:106:13)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getUserNFTs (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:410:35)\n    at getUserNFTs (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:330:42)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:16:41"}
{"level":"error","message":"Get user NFTs error: Failed to get user NFTs: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to get user NFTs: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.getUserNFTs (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:435:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getUserNFTs (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:330:18)","timestamp":"2025-07-08 19:16:41"}
{"level":"info","message":"Blockchain service initialized for amoy network","service":"spray-nft-backend","timestamp":"2025-07-08 19:17:25"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:17:25"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:17:25"}
{"level":"info","message":"Blockchain service initialized for amoy network","service":"spray-nft-backend","timestamp":"2025-07-08 19:18:05"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:18:05"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:18:05"}
{"level":"info","message":"Blockchain service initialized for amoy network","service":"spray-nft-backend","timestamp":"2025-07-08 19:18:16"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 19:18:16"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 19:18:16"}
