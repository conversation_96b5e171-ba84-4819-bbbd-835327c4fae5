{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:15:16"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:18:31"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:18:31"}
{"_message":"User validation failed","errors":{"wallet.address":{"kind":"required","message":"Path `wallet.address` is required.","name":"ValidatorError","path":"wallet.address","properties":{"message":"Path `wallet.address` is required.","path":"wallet.address","type":"required"}}},"level":"error","message":"Registration error: User validation failed: wallet.address: Path `wallet.address` is required.","service":"spray-nft-backend","stack":"ValidationError: User validation failed: wallet.address: Path `wallet.address` is required.\n    at Document.invalidate (/Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/document.js:3352:32)\n    at /Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/document.js:3113:17\n    at /Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-07-08 18:20:57"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:23"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:23"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:36"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:21:36"}
{"level":"info","message":"New user registered: <EMAIL>","service":"spray-nft-backend","timestamp":"2025-07-08 18:40:56"}
{"level":"info","message":"User logged in: <EMAIL>","service":"spray-nft-backend","timestamp":"2025-07-08 18:41:54"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"rpc-mumbai.maticvigil.com","level":"error","message":"Get wallet info error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com","service":"spray-nft-backend","stack":"Error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:118:26)","syscall":"getaddrinfo","timestamp":"2025-07-08 18:42:24"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"rpc-mumbai.maticvigil.com","level":"error","message":"Get wallet info error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com","service":"spray-nft-backend","stack":"Error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:118:26)","syscall":"getaddrinfo","timestamp":"2025-07-08 18:43:19"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:43:51"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:43:51"}
{"code":"SERVER_ERROR","info":{"requestUrl":"https://rpc.ankr.com/polygon_mumbai","responseBody":"{\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","responseStatus":"403 Forbidden"},"level":"error","message":"Get wallet info error: server response 403 Forbidden (request={  }, response={  }, error=null, info={ \"requestUrl\": \"https://rpc.ankr.com/polygon_mumbai\", \"responseBody\": \"{\\\"error\\\":\\\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\\\"}\", \"responseStatus\": \"403 Forbidden\" }, code=SERVER_ERROR, version=6.15.0)","request":{},"response":{},"service":"spray-nft-backend","shortMessage":"server response 403 Forbidden","stack":"Error: server response 403 Forbidden (request={  }, response={  }, error=null, info={ \"requestUrl\": \"https://rpc.ankr.com/polygon_mumbai\", \"responseBody\": \"{\\\"error\\\":\\\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\\\"}\", \"responseStatus\": \"403 Forbidden\" }, code=SERVER_ERROR, version=6.15.0)\n    at makeError (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/errors.js:137:21)\n    at assert (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/errors.js:157:15)\n    at FetchResponse.assertOk (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/fetch.js:839:32)\n    at JsonRpcProvider._send (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:917:18)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:281:40","timestamp":"2025-07-08 18:44:30"}
{"level":"info","message":"MongoDB Connected: localhost","service":"spray-nft-backend","timestamp":"2025-07-08 18:45:07"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"spray-nft-backend","timestamp":"2025-07-08 18:45:07"}
