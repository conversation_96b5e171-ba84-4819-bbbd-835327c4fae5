{"address": "::", "code": "EADDRINUSE", "errno": -48, "level": "error", "message": "Uncaught Exception: listen EADDRINUSE: address already in use :::3000", "port": 3000, "service": "spray-nft-backend", "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at Function.listen (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/application.js:635:24)\n    at startServer (/Users/<USER>/Documents/sprayNft/src/server.js:82:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)", "syscall": "listen", "timestamp": "2025-07-08 18:15:16"}