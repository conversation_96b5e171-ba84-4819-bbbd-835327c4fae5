{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"spray-nft-backend","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at Function.listen (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/application.js:635:24)\n    at startServer (/Users/<USER>/Documents/sprayNft/src/server.js:82:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-08 18:15:16"}
{"_message":"User validation failed","errors":{"wallet.address":{"kind":"required","message":"Path `wallet.address` is required.","name":"ValidatorError","path":"wallet.address","properties":{"message":"Path `wallet.address` is required.","path":"wallet.address","type":"required"}}},"level":"error","message":"Registration error: User validation failed: wallet.address: Path `wallet.address` is required.","service":"spray-nft-backend","stack":"ValidationError: User validation failed: wallet.address: Path `wallet.address` is required.\n    at Document.invalidate (/Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/document.js:3352:32)\n    at /Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/document.js:3113:17\n    at /Users/<USER>/Documents/sprayNft/node_modules/mongoose/lib/schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-07-08 18:20:57"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"rpc-mumbai.maticvigil.com","level":"error","message":"Get wallet info error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com","service":"spray-nft-backend","stack":"Error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:118:26)","syscall":"getaddrinfo","timestamp":"2025-07-08 18:42:24"}
{"code":"ENOTFOUND","errno":-3008,"hostname":"rpc-mumbai.maticvigil.com","level":"error","message":"Get wallet info error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com","service":"spray-nft-backend","stack":"Error: getaddrinfo ENOTFOUND rpc-mumbai.maticvigil.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:118:26)","syscall":"getaddrinfo","timestamp":"2025-07-08 18:43:19"}
{"code":"SERVER_ERROR","info":{"requestUrl":"https://rpc.ankr.com/polygon_mumbai","responseBody":"{\"error\":\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\"}","responseStatus":"403 Forbidden"},"level":"error","message":"Get wallet info error: server response 403 Forbidden (request={  }, response={  }, error=null, info={ \"requestUrl\": \"https://rpc.ankr.com/polygon_mumbai\", \"responseBody\": \"{\\\"error\\\":\\\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\\\"}\", \"responseStatus\": \"403 Forbidden\" }, code=SERVER_ERROR, version=6.15.0)","request":{},"response":{},"service":"spray-nft-backend","shortMessage":"server response 403 Forbidden","stack":"Error: server response 403 Forbidden (request={  }, response={  }, error=null, info={ \"requestUrl\": \"https://rpc.ankr.com/polygon_mumbai\", \"responseBody\": \"{\\\"error\\\":\\\"message: API key is not allowed to access blockchain, json-rpc code: -32052, rest code: 403\\\"}\", \"responseStatus\": \"403 Forbidden\" }, code=SERVER_ERROR, version=6.15.0)\n    at makeError (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/errors.js:137:21)\n    at assert (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/errors.js:157:15)\n    at FetchResponse.assertOk (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/utils/fetch.js:839:32)\n    at JsonRpcProvider._send (/Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:917:18)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /Users/<USER>/Documents/sprayNft/node_modules/ethers/lib.commonjs/providers/provider-jsonrpc.js:281:40","timestamp":"2025-07-08 18:44:30"}
{"error":{"details":"Invalid API key provided: not found","reason":"INVALID_API_KEYS"},"level":"error","message":"Pinata connection test failed:","service":"spray-nft-backend","timestamp":"2025-07-08 18:57:45"}
{"level":"error","message":"Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:83:15)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getContractInfo (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:375:35)\n    at getContractInfo (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:362:50)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:13:21"}
{"level":"error","message":"Failed to get contract info: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:106:13)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getContractInfo (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:375:35)\n    at getContractInfo (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:362:50)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:13:21"}
{"level":"error","message":"Get contract info error: Failed to get contract info: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to get contract info: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.getContractInfo (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:401:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getContractInfo (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:362:26)","timestamp":"2025-07-08 19:13:21"}
{"level":"error","message":"Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:83:15)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getUserNFTs (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:410:35)\n    at getUserNFTs (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:330:42)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:16:41"}
{"level":"error","message":"Failed to get user NFTs: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.loadContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:106:13)\n    at BlockchainService.getContract (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:117:18)\n    at BlockchainService.getUserNFTs (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:410:35)\n    at getUserNFTs (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:330:42)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/sprayNft/node_modules/express/lib/router/route.js:149:13)\n    at auth (/Users/<USER>/Documents/sprayNft/src/middleware/auth.js:64:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-08 19:16:41"}
{"level":"error","message":"Get user NFTs error: Failed to get user NFTs: Failed to load contract: Contract address not configured for amoy","service":"spray-nft-backend","stack":"Error: Failed to get user NFTs: Failed to load contract: Contract address not configured for amoy\n    at BlockchainService.getUserNFTs (/Users/<USER>/Documents/sprayNft/src/services/blockchainService.js:435:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getUserNFTs (/Users/<USER>/Documents/sprayNft/src/controllers/nftController.js:330:18)","timestamp":"2025-07-08 19:16:41"}
